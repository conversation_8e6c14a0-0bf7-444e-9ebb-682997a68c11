/* 好业财风格主题样式 - Finance Plus Theme */

/* CSS变量定义 */
:root {
    --text-primary-color: #333;
    --ThemeWhiteColor: #ffffff;
    --ThemePrimaryColor: #1890ff;
    --ThemePrimaryColorHover: #40a9ff;
    --ThemePrimaryColorActive: #096dd9;
    --ThemeSuccessColor: #52c41a;
    --ThemeErrorColor: #ff4d4f;
    --ThemeWarnColor: #faad14;
    --ThemeTextSencondaryColor: #666;
    --ThemeBgGlobalColor: #f5f5f5;
    --ThemeColorAppPageBG__: #fafafa;
}

/* 全局字体和基础样式 */
.finance-plus-app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    font-size: 12px;
    box-sizing: border-box;
    color: var(--text-primary-color);
    line-height: 1.6666666666666667;
}

/* 主窗口样式 */
.finance-plus-window {
    background: var(--ThemeWhiteColor);
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 32px);
}

/* 窗口标题栏 */
.finance-plus-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;
}

.finance-plus-title {
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.finance-plus-title .icon {
    font-size: 16px;
}

.finance-plus-controls {
    display: flex;
    gap: 8px;
}

.finance-plus-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.finance-plus-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
}

/* 工具栏样式 */
.finance-plus-toolbar {
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    padding: 8px 16px;
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.toolbar-btn {
    background: var(--ThemeWhiteColor);
    border: 1px solid #d9d9d9;
    color: var(--text-primary-color);
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    min-height: 28px;
}

.toolbar-btn:hover {
    border-color: var(--ThemePrimaryColor);
    color: var(--ThemePrimaryColor);
    text-decoration: none;
}

.toolbar-btn.primary {
    background: var(--ThemePrimaryColor);
    color: white;
    border-color: var(--ThemePrimaryColor);
}

.toolbar-btn.primary:hover {
    background: var(--ThemePrimaryColorHover);
    border-color: var(--ThemePrimaryColorHover);
    color: white;
}

.toolbar-btn.success {
    background: var(--ThemeSuccessColor);
    color: white;
    border-color: var(--ThemeSuccessColor);
}

.toolbar-btn.danger {
    background: var(--ThemeErrorColor);
    color: white;
    border-color: var(--ThemeErrorColor);
}

.toolbar-separator {
    width: 1px;
    height: 20px;
    background: #e8e8e8;
    margin: 0 8px;
}

.toolbar-status {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
    color: var(--ThemeTextSencondaryColor);
}

/* 表格样式 */
.finance-plus-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.finance-plus-table th {
    background: #fafafa;
    border: 1px solid #e8e8e8;
    padding: 8px 4px;
    text-align: center;
    font-weight: 500;
    color: var(--text-primary-color);
    font-size: 12px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.finance-plus-table td {
    border: 1px solid #e8e8e8;
    padding: 6px 4px;
    vertical-align: middle;
    background: white;
    font-size: 12px;
}

.finance-plus-table tbody tr:hover {
    background: #f5f5f5;
}

.finance-plus-table tbody tr.selected {
    background: #e6f7ff;
}

/* 状态徽章 */
.status-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
}

.status-draft {
    background: #f0f0f0;
    color: #666;
}

.status-pending {
    background: #fff7e6;
    color: var(--ThemeWarnColor);
}

.status-approved {
    background: #f6ffed;
    color: var(--ThemeSuccessColor);
}

.status-posted {
    background: #e6f7ff;
    color: var(--ThemePrimaryColor);
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 2px;
    justify-content: center;
}

.action-btn {
    background: none;
    border: none;
    padding: 2px 4px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 12px;
    text-decoration: none;
    color: var(--text-primary-color);
    transition: all 0.2s;
    min-width: 20px;
    text-align: center;
}

.action-btn:hover {
    background: #e6f7ff;
    color: var(--ThemePrimaryColor);
    text-decoration: none;
}

.action-edit:hover {
    background: #e6f7ff;
    color: var(--ThemePrimaryColor);
}

.action-view:hover {
    background: #f0f9ff;
    color: #1890ff;
}

.action-delete:hover {
    background: #fff2f0;
    color: var(--ThemeErrorColor);
}

.action-approve:hover {
    background: #f6ffed;
    color: var(--ThemeSuccessColor);
}

.action-reject:hover {
    background: #fff2f0;
    color: var(--ThemeErrorColor);
}

/* 表单控件 */
.finance-plus-input {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    background: white;
    outline: none;
    transition: border-color 0.2s;
}

.finance-plus-input:focus {
    border-color: var(--ThemePrimaryColor);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.finance-plus-select {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    background: white;
    outline: none;
    cursor: pointer;
}

/* 状态栏 */
.status-bar {
    background: #fafafa;
    border-top: 1px solid #e8e8e8;
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--ThemeTextSencondaryColor);
}

/* 分页样式 */
.finance-plus-pagination {
    padding: 12px 16px;
    display: flex;
    justify-content: center;
    gap: 4px;
    border-top: 1px solid #e8e8e8;
    background: #fafafa;
}

.page-link {
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    background: white;
    color: var(--text-primary-color);
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s;
}

.page-link:hover {
    border-color: var(--ThemePrimaryColor);
    color: var(--ThemePrimaryColor);
    text-decoration: none;
}

.page-link.active {
    background: var(--ThemePrimaryColor);
    color: white;
    border-color: var(--ThemePrimaryColor);
}

.page-link.disabled {
    background: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--ThemeTextSencondaryColor);
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.empty-text {
    font-size: 16px;
    margin-bottom: 8px;
    color: var(--text-primary-color);
}

.empty-hint {
    font-size: 12px;
    color: var(--ThemeTextSencondaryColor);
}
