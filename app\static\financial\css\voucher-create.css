/* 财务凭证创建页面专用样式 */

/* 基础容器样式 */
.voucher-container, .voucher-container * {
    font-size: 13px !important;
}

/* 用友风格科目选择器样式 */
.uf-subject-selector, .uf-subject-selector * {
    font-size: 13px !important;
}

.uf-subject-selector {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    width: 800px;
    height: 600px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-family: var(--uf-font-family);
    font-size: 13px;
}

.uf-window {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.uf-window-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: linear-gradient(to bottom, #e6f7ff, #bae7ff);
    border-bottom: 1px solid #91d5ff;
    border-radius: 4px 4px 0 0;
}

.uf-window-title {
    display: flex;
    align-items: center;
    font-weight: bold;
    color: #1890ff;
    font-size: 13px;
}

.uf-window-title i {
    margin-right: 6px;
    font-size: 13px;
}

.uf-window-controls {
    display: flex;
    gap: 4px;
}

.uf-btn-minimize,
.uf-btn-close {
    width: 20px;
    height: 20px;
    border: none;
    background: #fff;
    border-radius: 2px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.uf-btn-minimize:hover {
    background: #e6f7ff;
}

.uf-btn-close:hover {
    background: #ff4d4f;
    color: #fff;
}

.uf-toolbar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    gap: 8px;
    font-size: 13px;
}

.uf-toolbar-group {
    display: flex;
    gap: 6px;
}

.uf-toolbar-separator {
    width: 1px;
    height: 30px;
    background: #d9d9d9;
    margin: 0 8px;
}

.uf-btn {
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    background: #fff;
    border-radius: 2px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
}

.uf-btn:hover {
    border-color: #40a9ff;
    color: #1890ff;
}

.uf-btn-primary {
    background: #1890ff;
    color: #fff;
    border-color: #1890ff;
}

.uf-btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
}

.uf-search-group {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.uf-search-input {
    width: 200px;
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 2px 0 0 2px;
    font-size: 13px;
    outline: none;
}

.uf-search-input:focus {
    border-color: #40a9ff;
}

.uf-btn-search {
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-left: none;
    background: #fff;
    border-radius: 0 2px 2px 0;
    cursor: pointer;
    transition: all 0.2s;
}

.uf-btn-search:hover {
    background: #e6f7ff;
    border-color: #40a9ff;
}

.uf-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.uf-subject-panel {
    display: flex;
    width: 100%;
    height: 100%;
}

.uf-subject-tree {
    width: 40%;
    border-right: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    font-size: 13px;
}

.uf-tree-header {
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    font-weight: bold;
    font-size: 13px;
    color: #666;
}

.uf-tree-content {
    flex: 1;
    overflow-y: auto;
    padding: 4px;
}

.uf-tree-node {
    display: flex;
    align-items: center;
    padding: 2px 4px;
    cursor: pointer;
    font-size: 13px;
    line-height: 20px;
    border-radius: 2px;
    transition: background 0.2s;
}

.uf-tree-node:hover {
    background: #e6f7ff;
}

.uf-tree-node.selected {
    background: #bae7ff;
    color: #1890ff;
}

.uf-tree-expand {
    width: 16px;
    text-align: center;
    cursor: pointer;
    user-select: none;
}

.uf-tree-icon {
    margin-right: 4px;
    font-size: 13px;
}

.uf-tree-text {
    flex: 1;
    cursor: pointer;
}

.uf-subject-details {
    width: 40%;
    display: flex;
    flex-direction: column;
}

.uf-details-header {
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    font-weight: bold;
    font-size: 13px;
    color: #666;
}

.uf-details-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
}

.uf-detail-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 13px;
}

.uf-detail-item label {
    width: 80px;
    color: #666;
    margin-right: 8px;
}

.uf-detail-item span {
    color: #222;
    font-weight: 500;
}

.uf-statusbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 12px;
    background: #fafafa;
    border-top: 1px solid #e8e8e8;
    font-size: 13px;
    color: #666;
}

.uf-no-results {
    text-align: center;
    padding: 20px;
    color: #999;
    font-size: 13px;
}

.search-result {
    background: #fff7e6;
    border-left: 3px solid #faad14;
}

.search-result:hover {
    background: #fff1b8;
}

.uf-search-type-header {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    background: #f0f9ff;
    border-bottom: 1px solid #e6f7ff;
    margin: 2px 0;
    font-size: 13px;
}

.uf-type-group {
    background: #f6ffed;
    border-left: 3px solid #52c41a;
}

.uf-system-subject {
    background: #f6ffed;
}

.uf-school-subject {
    background: #f9f0ff;
}

/* 图标字体 */
.uf-icon-folder::before { content: "📁"; }
.uf-icon-check::before { content: "✓"; }
.uf-icon-close::before { content: "✕"; }
.uf-icon-expand::before { content: "⊞"; }
.uf-icon-collapse::before { content: "⊟"; }
.uf-icon-search::before { content: "🔍"; }

/* 用友风格专业凭证编辑器样式 - 与列表页面保持一致 */
.voucher-container {
    background: #f5f7fa;
    min-height: 100vh;
    padding: 10px;
}

.voucher-window {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(30, 136, 229, 0.1);
    margin: 0 auto;
}

.voucher-header {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    border-bottom: 1px solid #90caf9;
    padding: 8px 15px;
    font-size: 13px;
    font-weight: bold;
    color: #1565c0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.voucher-toolbar {
    background: #f8f8f8;
    border-bottom: 1px solid #e0e0e0;
    padding: 5px 10px;
    display: flex;
    gap: 5px;
    align-items: center;
    flex-wrap: wrap;
}

.toolbar-btn {
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    min-width: 60px;
    text-align: center;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.toolbar-btn:hover {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    color: #1565c0;
    text-decoration: none;
}

.toolbar-btn.primary {
    background: linear-gradient(to bottom, #1e88e5, #1565c0);
    color: white;
    border-color: #1565c0;
}

.toolbar-btn.primary:hover {
    background: linear-gradient(to bottom, #1565c0, #0d47a1);
    color: white;
}

.voucher-info-bar {
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    font-size: 13px;
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    gap: 15px;
}

.info-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.info-label {
    color: #666;
    font-weight: normal;
    font-size: 13px;
    font-family: '宋体', 'SimSun', serif;
    white-space: nowrap;
}

.info-input {
    border: 1px solid #e0e0e0;
    padding: 2px 5px;
    font-size: 13px;
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;
    background: white;
    border-radius: 3px;
}

.info-input:focus {
    border-color: #333;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
    outline: none;
}

/* 简洁的凭证字号样式 - 去除多余边框 */
.voucher-type-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.voucher-type-label {
    font-size: 14px;
    font-weight: bold;
    color: #000;
    font-family: '宋体', 'SimSun', serif;
}

.voucher-type-select {
    font-size: 14px;
    font-weight: bold;
    color: #000;
    font-family: '宋体', 'SimSun', serif;
    border: 1px solid #999;
    background: white;
    outline: none;
    min-width: 40px;
    padding: 2px 4px;
    border-radius: 2px;
}

.voucher-number-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.voucher-number-label {
    font-size: 14px;
    font-weight: bold;
    color: #000;
    font-family: '宋体', 'SimSun', serif;
}

.voucher-number-input {
    font-size: 14px;
    font-weight: bold;
    color: #000;
    font-family: 'Times New Roman', serif;
    border: 1px solid #999;
    background: white;
    outline: none;
    min-width: 120px;
    text-align: center;
    padding: 2px 4px;
    border-radius: 2px;
}

.voucher-table-container {
    padding: 0;
    background: white;
    overflow-x: auto;
    overflow-y: visible;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.voucher-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    margin: 0;
    table-layout: fixed;
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    min-width: 800px; /* 确保表格最小宽度 */
}

.voucher-table th {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    border: 1px solid #90caf9;
    padding: 6px 4px;
    text-align: center;
    font-weight: normal;
    color: #1565c0;
    font-size: 13px;
    white-space: nowrap;
}

.voucher-table td {
    border: 1px solid #e0e0e0;
    padding: 4px 6px;
    vertical-align: middle;
    background: white;
    font-size: 13px;
    line-height: 1.4;
    overflow: hidden;
}

.voucher-table tbody tr:hover {
    background: #f5f5f5;
}

.voucher-table tbody tr.selected {
    background: #e3f2fd;
}

/* 这些样式已移动到文件末尾的优化部分 */

/* 金额输入框用友风格 - 与列表页面保持一致 */
.amount-input {
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;
    font-weight: bold;
    text-align: right;
    font-size: 13px;
    color: #000;
    letter-spacing: 0.5px;
    width: 100%;
    border: 1px solid transparent;
    background: transparent;
}

.amount-input:focus {
    background: #fff7e6;
    border: 1px solid #faad14;
    outline: none;
}

.line-number {
    text-align: center;
    color: #666;
    background: #f8f8f8;
    width: 50px;
    font-size: 13px;
    font-weight: bold;
    padding: 8px 4px;
}

.subject-cell {
    position: relative;
    min-width: 200px;
}

.subject-selector {
    display: flex;
    align-items: center;
    gap: 4px;
    width: 100%;
}

.subject-code {
    width: 80px;
    min-width: 80px;
    text-align: center;
    font-size: 13px;
    font-weight: bold;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 2px;
    padding: 2px 4px;
}

.subject-name {
    flex: 1;
    font-size: 13px;
    min-width: 120px;
}

.subject-btn {
    background: #f0f0f0;
    border: 1px solid #e0e0e0;
    padding: 2px 4px;
    cursor: pointer;
    font-size: 13px;
}

.totals-row {
    background: #e3f2fd;
    font-weight: bold;
    font-size: 13px;
}

.totals-row td {
    border-top: 2px solid #1e88e5;
    color: #1565c0;
}

.balance-status {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 13px;
    font-weight: bold;
}

.balance-ok {
    background: #e8f5e9;
    color: #2e7d32;
}

.balance-error {
    background: #ffebee;
    color: #c62828;
}

.signature-area {
    background: #f8f8f8;
    border-top: 1px solid #e0e0e0;
    padding: 10px 15px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    font-size: 13px;
}

.signature-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.signature-label {
    color: #666;
    min-width: 40px;
}

.signature-box {
    border-bottom: 1px solid #999;
    min-width: 80px;
    height: 20px;
    display: inline-block;
}

/* 状态栏 - 与列表页面保持一致 */
.status-bar {
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    padding: 4px 15px;
    font-size: 13px;
    color: #666;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 用友经典货币符号 */
.uf-currency {
    font-family: 'Times New Roman', serif;
    font-weight: bold;
    color: #000;
}

/* 模式特定样式 */
.mode-view .cell-input,
.mode-view .info-input {
    border: none;
    background: transparent;
    pointer-events: none;
    color: #333;
}

.mode-view .toolbar-btn.edit-only {
    display: none;
}

.mode-view .toolbar-btn.view-only {
    display: inline-block;
}

.mode-edit .toolbar-btn.view-only {
    display: none;
}

.mode-edit .toolbar-btn.edit-only {
    display: inline-block;
}

/* 审核状态标签 */
.voucher-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 13px;
    font-weight: normal;
    margin-left: 10px;
}

.voucher-status.draft {
    background-color: #e0e0e0;
    color: #616161;
}

.voucher-status.pending {
    background-color: #fff8e1;
    color: #ff8f00;
}

.voucher-status.approved {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.voucher-status.posted {
    background-color: #e3f2fd;
    color: #1565c0;
}

/* 新增的特定样式类 */
.toolbar-balance-indicator {
    margin-left: auto;
}

.voucher-date-input {
    width: 120px;
}

.attachment-count-input {
    width: 40px;
}

/* 表格列宽样式 - 优化对齐 */
.col-sequence {
    width: 50px;
    min-width: 50px;
    max-width: 50px;
    text-align: center;
}

.col-summary {
    width: 25%;
    min-width: 180px;
}

.col-subject {
    width: 35%;
    min-width: 220px;
}

.col-debit {
    width: 15%;
    min-width: 100px;
    text-align: right;
}

.col-credit {
    width: 15%;
    min-width: 100px;
    text-align: right;
}

/* 合计行样式 */
.totals-label {
    text-align: center;
    font-weight: bold;
    font-family: '宋体', 'SimSun', serif;
}

.totals-amount {
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;
    font-weight: bold;
    text-align: right;
}

/* 科目选择器默认隐藏 */
.uf-subject-selector {
    display: none;
}

/* 表格内容对齐优化 */
.voucher-table td:nth-child(1) {
    text-align: center; /* 序号居中 */
}

.voucher-table td:nth-child(2) {
    text-align: left; /* 摘要左对齐 */
}

.voucher-table td:nth-child(3) {
    text-align: left; /* 科目左对齐 */
}

.voucher-table td:nth-child(4),
.voucher-table td:nth-child(5) {
    text-align: right; /* 金额右对齐 */
    padding-right: 8px;
    border: none; /* 删除借方金额和贷方金额列的表线条 */
}

/* 删除借方金额和贷方金额列表头的边框 */
.voucher-table th:nth-child(4),
.voucher-table th:nth-child(5) {
    border: none;
}

/* 摘要输入框优化 */
.summary-input {
    width: 100%;
    min-height: 36px;
    resize: vertical;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    padding: 4px 6px;
    border: 1px solid transparent;
    background: transparent;
    font-size: 13px;
    line-height: 1.4;
}

.summary-input:focus {
    background: #f0f8ff;
    border: 1px solid #1890ff;
    outline: none;
}

/* 表格行高度统一 */
.voucher-table tbody tr {
    height: 44px;
    min-height: 44px;
}

/* 输入框通用样式优化 */
.cell-input {
    border: none;
    background: transparent;
    width: 100%;
    padding: 0px 0px;
    font-size: 13px;
    outline: none;
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.cell-input:focus {
    background: #e3f2fd;
    border: 1px solid #1e88e5;
    border-radius: 2px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .voucher-table {
        min-width: 700px;
    }

    .col-summary {
        width: 20%;
        min-width: 150px;
    }

    .col-subject {
        width: 30%;
        min-width: 180px;
    }
}

@media (max-width: 768px) {
    .voucher-info-bar {
        flex-direction: column;
        gap: 10px;
    }

    .signature-area {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .voucher-table {
        font-size: 12px;
        min-width: 600px;
    }

    .col-sequence {
        width: 40px;
        min-width: 40px;
    }

    .col-summary {
        width: 25%;
        min-width: 120px;
    }

    .col-subject {
        width: 35%;
        min-width: 150px;
    }

    .col-debit,
    .col-credit {
        width: 15%;
        min-width: 80px;
    }

    .subject-code {
        width: 60px;
        min-width: 60px;
    }
}
