/* 用友财务软件专业主题样式 v2.0 */
:root {
    /* 用友企业级色彩体系 */
    --uf-primary: #165DFF;
    --uf-primary-dark: #0D47A1;
    --uf-primary-light: #4080FF;
    --uf-primary-gradient: linear-gradient(135deg, var(--uf-primary), var(--uf-primary-light));
    --uf-success: #00B42A;
    --uf-warning: #FF7D00;
    --uf-danger: #F53F3F;
    --uf-info: #0088CC;
    --uf-light: #f8f9fa;
    --uf-dark: #343a40;
    --uf-muted: #6c757d;
    --uf-border: #c0c0c0;
    --uf-grid-border: #999999;
    --uf-header-bg: #e6f2ff;
    --uf-toolbar-bg: #f0f8ff;
    --uf-row-hover: #f0f8ff;
    --uf-selected: #cce5ff;
    --uf-input-focus: #fffef7;
    --uf-disabled-bg: #f5f5f5;
    --uf-disabled-text: #999999;
    /* 新增统一网格变量 */
    --uf-grid-gap: 16px;
    --uf-breakpoint-sm: 576px;
    --uf-breakpoint-md: 768px;
    --uf-breakpoint-lg: 992px;
    
    /* 用友标准字体 */
    --uf-font-family: 'Microsoft YaHei', 'SimSun', '宋体', Arial, sans-serif;
    --uf-font-size: 13px;
    --uf-font-size-large: 14px;
    --uf-font-size-small: 12px;
    --uf-line-height: 1.3;
    
    /* 用友专业尺寸 */
    --uf-border-radius: 1px;
    --uf-btn-height: 36px;
    --uf-btn-height-sm: 32px;
    --uf-btn-height-lg: 44px;
    --uf-table-row-height: 36px;
    --uf-table-header-height: 36px;
    --uf-card-padding: 12px;
    --uf-form-label-width: 90px;
    
    /* 用友专业阴影和过渡 */
    --uf-box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    --uf-box-shadow-hover: 0 2px 4px rgba(0,102,204,0.2);
    --uf-box-shadow-focus: 0 0 0 2px rgba(0,102,204,0.3);
    --uf-transition: all 0.15s ease;

    /* 中性色体系优化 */
    --uf-white: #FFFFFF;
    --uf-gray-50: #F7F8FA;
    --uf-gray-100: #F2F3F5;
    --uf-gray-200: #E5E6EB;
    --uf-gray-300: #C9CDD4;
    --uf-gray-400: #86909C;
    --uf-gray-500: #4E5969;
    --uf-gray-600: #272E3B;
    --uf-gray-700: #1D2129;
    /* 数据色彩体系 */
    --uf-amount-positive: #00B42A;
    --uf-amount-negative: #F53F3F;
    --uf-amount-zero: #86909C;
}

/* 用友财务软件基础样式 */
body {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
    color: var(--uf-gray-600);
    background-color: var(--uf-gray-50);
    margin: 0;
    padding: 0;
}

/* 用友专业按钮样式 */
.uf-btn {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: 500;
    padding: 0 14px;
    border: 1px solid var(--uf-border);
    border-radius: 4px;
    background: var(--uf-white);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    color: #333;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    text-decoration: none;
    transition: var(--uf-transition);
    min-height: var(--uf-btn-height);
    background: linear-gradient(to bottom, var(--uf-white), var(--uf-gray-50));
    vertical-align: middle;
    user-select: none;
    -webkit-user-select: none;
    line-height: 1.4;
}

.uf-btn:hover {
    background: linear-gradient(to bottom, var(--uf-gray-50), var(--uf-white));
    border-color: var(--uf-primary);
    color: var(--uf-primary);
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(22, 93, 255, 0.2);
}

.uf-btn:active {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
    transform: translateY(1px);
}

.uf-btn:focus {
    outline: none;
    box-shadow: var(--uf-box-shadow-focus);
}

.uf-btn:disabled {
    background: var(--uf-disabled-bg);
    color: var(--uf-disabled-text);
    border-color: #ddd;
    cursor: not-allowed;
    box-shadow: none;
    opacity: 0.7;
}

/* 用友按钮颜色变体 */
.uf-btn-primary {
    background: var(--uf-primary-gradient);
    color: var(--uf-white);
    border: none;
}

.uf-btn-primary:hover {
    background: linear-gradient(to bottom, var(--uf-primary), #3672FF);
    box-shadow: 0 4px 12px rgba(22, 93, 255, 0.2);
}

.uf-btn-primary:active {
    background: var(--uf-primary-dark);
}

.uf-btn-success {
    background: linear-gradient(to bottom, var(--uf-success) 0%, #155724 100%);
    border-color: #155724;
    color: white;
    font-weight: 500;
}

.uf-btn-warning {
    background: linear-gradient(to bottom, var(--uf-warning) 0%, #b8860b 100%);
    border-color: #b8860b;
    color: #333;
    font-weight: 500;
}

.uf-btn-danger {
    background: linear-gradient(to bottom, var(--uf-danger) 0%, #a71e2a 100%);
    border-color: #a71e2a;
    color: white;
    font-weight: 500;
}

.uf-btn-info {
    background: linear-gradient(to bottom, var(--uf-info) 0%, #0f5460 100%);
    border-color: #0f5460;
    color: white;
    font-weight: 500;
}

/* 用友按钮尺寸变体 */
.uf-btn-sm {
    font-size: var(--uf-font-size-small);
    padding: 0 12px;
    min-height: var(--uf-btn-height-sm);
}

.uf-btn-lg {
    font-size: var(--uf-font-size-large);
    padding: 0 20px;
    min-height: var(--uf-btn-height-lg);
    font-weight: 500;
}

/* 用友按钮组样式 */
.uf-btn-group {
    display: inline-flex;
    gap: 0;
    position: relative;
}

.uf-btn-group .uf-btn {
    border-radius: 0;
    margin-left: -1px;
}

.uf-btn-group .uf-btn:first-child {
    border-radius: var(--uf-border-radius) 0 0 var(--uf-border-radius);
    margin-left: 0;
}

.uf-btn-group .uf-btn:last-child {
    border-radius: 0 var(--uf-border-radius) var(--uf-border-radius) 0;
}

.uf-btn-group .uf-btn:only-child {
    border-radius: var(--uf-border-radius);
}

.uf-btn-group .uf-btn:hover:not(:disabled) {
    z-index: 1;
}

.uf-btn-group .uf-btn:focus:not(:disabled) {
    z-index: 2;
}

/* 用友专业表格样式 */
.uf-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--uf-font-size);
    background: white;
    font-family: var(--uf-font-family);
    border: 1px solid var(--uf-grid-border);
}

.uf-table th {
    background: var(--uf-gray-50);
    border-bottom: 2px solid var(--uf-gray-200);
    color: var(--uf-gray-600);
    font-weight: 600;
    padding: 6px 8px;
    text-align: center;
    font-size: var(--uf-font-size);
    white-space: nowrap;
    height: var(--uf-table-header-height);
    vertical-align: middle;
    position: relative;
}

.uf-table th.sortable {
    cursor: pointer;
}

.uf-table th.sortable:hover {
    background: var(--uf-gray-50);
}

.uf-table th.sort-asc::after {
    content: "↑";
    position: absolute;
    right: 4px;
    font-size: 13px;
}

.uf-table th.sort-desc::after {
    content: "↓";
    position: absolute;
    right: 4px;
    font-size: 13px;
}

.uf-table td {
    border: 1px solid var(--uf-grid-border);
    padding: 6px 8px;
    vertical-align: middle;
    font-size: var(--uf-font-size);
    height: var(--uf-table-row-height);
    line-height: 1.4;
}

.uf-table tbody tr:hover {
    background: var(--uf-gray-50);
}

.uf-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.uf-table tbody tr:nth-child(even):hover {
    background: var(--uf-row-hover);
}

.uf-table tbody tr.selected {
    background: rgba(22, 93, 255, 0.08);
}

.uf-table .empty-row td {
    text-align: center;
    color: var(--uf-muted);
    padding: 16px;
}

/* 用友表格对齐样式 */
.uf-table .text-right {
    text-align: right;
}

.uf-table .text-center {
    text-align: center;
}

.uf-table .text-left {
    text-align: left;
}

/* 用友金额列样式 */
.uf-amount-col {
    text-align: right;
    padding-right: 12px !important;
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-size: var(--uf-font-size);
    white-space: nowrap;
}

.uf-amount {
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-weight: 500;
    font-size: var(--uf-font-size);
    text-align: right;
    color: #333;
    white-space: nowrap;
    letter-spacing: 0.5px;
}

.uf-currency {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    margin-right: 1px;
}

/* 用友表单样式 */
.uf-form-row {
    display: flex;
    gap: 16px;
    align-items: flex-end;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.uf-form-group {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 200px;
}

.uf-form-label {
    font-weight: 500;
    color: #333;
    font-size: var(--uf-font-size) !important;
    margin-right: 6px;
    min-width: var(--uf-form-label-width);
    text-align: right;
    white-space: nowrap;
}

.uf-form-control {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size) !important;
    padding: 6px 12px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: white;
    color: #333;
    flex: 1;
    min-height: var(--uf-btn-height);
    line-height: 1.4;
}

/* 确保所有表单控件都使用正确的字体大小 */
input.uf-form-control,
select.uf-form-control,
textarea.uf-form-control {
    font-size: var(--uf-font-size) !important;
    font-family: var(--uf-font-family) !important;
}

.uf-form-control:focus {
    border-color: var(--uf-primary);
    outline: none;
    box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.25);
    background: var(--uf-input-focus);
}

.uf-form-control:disabled {
    background: var(--uf-disabled-bg);
    color: var(--uf-disabled-text);
    cursor: not-allowed;
}

.uf-form-control[readonly] {
    background: #f9f9f9;
    cursor: default;
}

.uf-form-hint {
    font-size: var(--uf-font-size-small);
    color: var(--uf-muted);
    margin-left: 8px;
}

.uf-form-error {
    font-size: var(--uf-font-size-small);
    color: var(--uf-danger);
    margin-left: 8px;
}

.uf-form-control.error {
    border-color: var(--uf-danger);
    box-shadow: 0 0 2px rgba(189, 33, 48, 0.3);
}

/* 用友卡片样式 */
.uf-card {
    background: var(--uf-white);
    border: 1px solid var(--uf-gray-200);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    margin-bottom: 8px;
    overflow: hidden;
}

.uf-card:hover {
    box-shadow: 0 4px 12px rgba(22, 93, 255, 0.12);
    transform: translateY(-2px);
}

.uf-card-header {
    background: var(--uf-header-bg);
    border-bottom: 1px solid var(--uf-border);
    padding: 8px 12px;
    font-size: var(--uf-font-size-large);
    font-weight: 600;
    color: var(--uf-gray-700);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.uf-card-header-title {
    display: flex;
    align-items: center;
    gap: 4px;
}

.uf-card-header-icon {
    color: var(--uf-primary);
}

.uf-card-header-actions {
    display: flex;
    gap: 4px;
}

.uf-card-body {
    padding: var(--uf-card-padding);
}

.uf-card-footer {
    background: #f8f9fa;
    border-top: 1px solid var(--uf-border);
    padding: 8px 12px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* 用友专业分页样式 */
.uf-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
    padding: 8px;
    background: #f8f9fa;
    border-top: 1px solid var(--uf-grid-border);
}

.uf-page-item {
    display: inline-block;
}

.uf-page-link {
    display: block;
    padding: 2px 6px;
    font-size: var(--uf-font-size);
    color: var(--uf-primary);
    text-decoration: none;
    border: 1px solid var(--uf-border);
    background: white;
    border-radius: var(--uf-border-radius);
    min-width: 20px;
    text-align: center;
    transition: var(--uf-transition);
}

.uf-page-link:hover {
    background: var(--uf-row-hover);
    border-color: var(--uf-primary);
    text-decoration: none;
}

.uf-page-item.active .uf-page-link {
    background: var(--uf-primary);
    color: white;
    border-color: var(--uf-primary);
}

.uf-page-item.disabled .uf-page-link {
    color: #999;
    background: #f5f5f5;
    cursor: not-allowed;
}

.uf-pagination-info {
    margin-left: 16px;
    color: var(--uf-muted);
}

/* 用友状态标签样式 */
.uf-status {
    display: inline-block;
    padding: 1px 4px;
    font-size: var(--uf-font-size-small);
    font-weight: 500;
    border-radius: var(--uf-border-radius);
    border: 1px solid;
    white-space: nowrap;
}

.uf-status-approved {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.uf-status-pending {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.uf-status-rejected {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

/* 用友代码样式 */
.uf-code {
    font-family: 'Courier New', monospace;
    background: #f0f8ff;
    padding: 1px 3px;
    border-radius: var(--uf-border-radius);
    font-size: var(--uf-font-size-small);
    border: 1px solid #e6f2ff;
}

.uf-code-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.uf-code-success {
    background: #d4edda;
    border-color: #c3e6cb;
}

/* 用友空状态样式 */
.uf-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    background: #fafafa;
    border: 1px dashed var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-empty-state i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 16px;
    display: block;
}

.uf-empty-state p {
    margin: 0;
    font-size: var(--uf-font-size);
    color: #999;
}

/* 用友工具栏样式 */
.uf-toolbar {
    background: var(--uf-toolbar-bg);
    border: 1px solid var(--uf-border);
    padding: 6px 8px;
    margin-bottom: 8px;
    border-radius: var(--uf-border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--uf-font-size);
}

.uf-toolbar-left,
.uf-toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.uf-toolbar-title {
    font-weight: 600;
    color: var(--uf-primary);
    margin-right: 8px;
}

/* 用友面包屑导航 */
.uf-breadcrumb {
    background: var(--uf-light);
    border: 1px solid var(--uf-border);
    padding: 6px 12px;
    margin-bottom: 8px;
    border-radius: var(--uf-border-radius);
    font-size: var(--uf-font-size);
}

.uf-breadcrumb-item {
    display: inline;
    color: #666;
}

.uf-breadcrumb-item + .uf-breadcrumb-item::before {
    content: " > ";
    color: #999;
    margin: 0 4px;
}

.uf-breadcrumb-item.active {
    color: var(--uf-primary);
    font-weight: 500;
}

.uf-breadcrumb-item a {
    color: var(--uf-primary);
    text-decoration: none;
}

.uf-breadcrumb-item a:hover {
    text-decoration: underline;
}

/* 用友专业链接样式 */
a {
    color: var(--uf-primary);
    text-decoration: none;
    transition: var(--uf-transition);
    font-size: inherit;
}

a:hover {
    color: var(--uf-primary-dark);
    text-decoration: underline;
}

a:focus {
    outline: none;
    box-shadow: var(--uf-box-shadow-focus);
    border-radius: 1px;
}

a:active {
    color: var(--uf-primary-dark);
}

/* 用友链接变体 */
.uf-link {
    color: var(--uf-primary);
    text-decoration: none;
    font-size: var(--uf-font-size);
    transition: var(--uf-transition);
    cursor: pointer;
}

.uf-link:hover {
    color: var(--uf-primary-dark);
    text-decoration: underline;
}

.uf-link:focus {
    outline: none;
    box-shadow: var(--uf-box-shadow-focus);
    border-radius: 1px;
}

.uf-link-success {
    color: var(--uf-success);
}

.uf-link-success:hover {
    color: #155724;
}

.uf-link-warning {
    color: var(--uf-warning);
}

.uf-link-warning:hover {
    color: #b8860b;
}

.uf-link-danger {
    color: var(--uf-danger);
}

.uf-link-danger:hover {
    color: #a71e2a;
}

.uf-link-info {
    color: var(--uf-info);
}

.uf-link-info:hover {
    color: #0f5460;
}

/* 用友链接尺寸 */
.uf-link-sm {
    font-size: var(--uf-font-size-small);
}

.uf-link-lg {
    font-size: var(--uf-font-size-large);
    font-weight: 500;
}

/* 用友禁用链接 */
.uf-link:disabled,
.uf-link.disabled {
    color: #999;
    cursor: not-allowed;
    text-decoration: none;
}

.uf-link:disabled:hover,
.uf-link.disabled:hover {
    color: #999;
    text-decoration: none;
}

/* 用友表格内链接 */
.uf-table a {
    color: var(--uf-primary);
    text-decoration: none;
    font-size: inherit;
}

.uf-table a:hover {
    color: var(--uf-primary-dark);
    text-decoration: underline;
}

/* 用友导航链接 */
.uf-nav-link {
    color: #333;
    text-decoration: none;
    padding: 4px 8px;
    border-radius: var(--uf-border-radius);
    transition: var(--uf-transition);
    font-size: var(--uf-font-size);
}

.uf-nav-link:hover {
    background: var(--uf-row-hover);
    color: var(--uf-primary);
    text-decoration: none;
}

.uf-nav-link.active {
    background: var(--uf-primary);
    color: white;
}

.uf-nav-link.active:hover {
    background: var(--uf-primary-dark);
    color: white;
}

/* 用友按钮式链接 */
.uf-link-btn {
    display: inline-flex;
    align-items: center;
    gap: 3px;
    padding: 3px 8px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    color: #333;
    text-decoration: none;
    font-size: var(--uf-font-size);
    transition: var(--uf-transition);
    min-height: var(--uf-btn-height);
    box-shadow: var(--uf-box-shadow);
}

.uf-link-btn:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
    text-decoration: none;
    box-shadow: var(--uf-box-shadow-hover);
}

.uf-link-btn:active {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

/* 用友文本链接（无下划线） */
.uf-link-text {
    color: var(--uf-primary);
    text-decoration: none;
    font-size: var(--uf-font-size);
    cursor: pointer;
    transition: var(--uf-transition);
}

.uf-link-text:hover {
    color: var(--uf-primary-dark);
    text-decoration: none;
}

/* 用友外部链接样式 */
.uf-link-external::after {
    content: " ↗";
    font-size: var(--uf-font-size-small);
    color: #999;
    margin-left: 2px;
}

/* 用友下载链接样式 */
.uf-link-download::before {
    content: "⬇ ";
    font-size: var(--uf-font-size-small);
    color: var(--uf-success);
    margin-right: 2px;
}

/* 用友邮件链接样式 */
.uf-link-email::before {
    content: "✉ ";
    font-size: var(--uf-font-size-small);
    color: var(--uf-info);
    margin-right: 2px;
}

/* 用友电话链接样式 */
.uf-link-phone::before {
    content: "☎ ";
    font-size: var(--uf-font-size-small);
    color: var(--uf-warning);
    margin-right: 2px;
}

/* ========================================
   用友财务专业业务样式
   ======================================== */

/* 财务金额显示样式 */
.uf-financial-amount {
    font-family: 'SF Mono', 'Menlo', monospace;
    font-weight: 600;
    font-size: 12px;
    text-align: right;
    color: #000;
    white-space: nowrap;
    letter-spacing: 0.3px;
    padding-right: 8px;
}

.uf-financial-amount.positive {
    color: var(--uf-amount-positive);
}

.uf-financial-amount.negative {
    color: var(--uf-amount-negative);
}

.uf-financial-amount.zero {
    color: var(--uf-amount-zero);
}

.uf-financial-amount.large {
    font-size: 14px;
    font-weight: 700;
}

.uf-financial-amount.small {
    font-size: 13px;
}

/* 财务金额输入框 */
.uf-amount-input {
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-size: 12px;
    text-align: right;
    padding: 3px 8px 3px 5px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: white;
    color: #000;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.uf-amount-input:focus {
    border-color: var(--uf-primary);
    outline: none;
    box-shadow: 0 0 3px rgba(0, 102, 204, 0.3);
    background: var(--uf-input-focus);
}

.uf-amount-input.error {
    border-color: var(--uf-danger);
    background: #fff8f8;
}

.uf-amount-input::-webkit-inner-spin-button,
.uf-amount-input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.uf-amount-input[type="number"] {
    -moz-appearance: textfield;
}

/* 财务科目代码样式 */
.uf-subject-code {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    font-weight: 600;
    color: var(--uf-primary);
    background: #f0f8ff;
    padding: 1px 4px;
    border-radius: 2px;
    border: 1px solid #e6f2ff;
    white-space: nowrap;
}

/* 财务科目名称样式 */
.uf-subject-name {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

/* 财务凭证号样式 */
.uf-voucher-number {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: 600;
    color: var(--uf-primary);
    background: #f0f8ff;
    padding: 2px 6px;
    border-radius: 2px;
    border: 1px solid #e6f2ff;
    white-space: nowrap;
}

/* 财务日期样式 */
.uf-financial-date {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

/* 财务摘要样式 */
.uf-financial-summary {
    font-size: 12px;
    color: #333;
    line-height: 1.3;
    max-width: 200px;
    word-wrap: break-word;
}

/* 财务状态标签 */
.uf-financial-status {
    display: inline-block;
    padding: 2px 6px;
    font-size: 13px;
    font-weight: 600;
    border-radius: 2px;
    border: 1px solid;
    white-space: nowrap;
    text-align: center;
    min-width: 50px;
}

.uf-financial-status.draft {
    background: rgba(255, 125, 0, 0.1);
    color: var(--uf-warning);
    border-color: rgba(255, 125, 0, 0.2);
}

.uf-financial-status.approved {
    background: rgba(0, 180, 42, 0.1);
    color: var(--uf-success);
    border-color: rgba(0, 180, 42, 0.2);
}

.uf-financial-status.posted {
    background: rgba(22, 93, 255, 0.1);
    color: var(--uf-primary);
    border-color: rgba(22, 93, 255, 0.2);
}

.uf-financial-status.cancelled {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.uf-financial-status.pending {
    background: #e2e3e5;
    color: #383d41;
    border-color: #d6d8db;
}

.uf-financial-status.verified {
    background: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

/* 财务表格专用样式 */
.uf-financial-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--uf-font-size);
    background: white;
    font-family: var(--uf-font-family);
    border: 1px solid var(--uf-grid-border);
}

.uf-financial-table th {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border: 1px solid var(--uf-grid-border);
    padding: 6px 8px;
    text-align: center;
    font-weight: 600;
    color: var(--uf-primary);
    font-size: var(--uf-font-size);
    white-space: nowrap;
    height: var(--uf-table-header-height);
}

.uf-financial-table td {
    border: 1px solid var(--uf-grid-border);
    padding: 6px 8px;
    vertical-align: middle;
    font-size: var(--uf-font-size);
    height: var(--uf-table-row-height);
    line-height: 1.4;
}

.uf-financial-table tbody tr:hover {
    background: var(--uf-row-hover);
}

.uf-financial-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.uf-financial-table tbody tr:nth-child(even):hover {
    background: var(--uf-row-hover);
}

.uf-financial-table tbody tr.selected {
    background: var(--uf-selected);
}

/* 财务表格列宽控制 */
.uf-financial-table .col-date {
    width: 100px;
}

.uf-financial-table .col-voucher {
    width: 120px;
}

.uf-financial-table .col-subject-code {
    width: 80px;
}

.uf-financial-table .col-subject-name {
    width: 150px;
}

.uf-financial-table .col-amount {
    width: 120px;
    text-align: right;
}

.uf-financial-table .col-summary {
    min-width: 200px;
}

.uf-financial-table .col-status {
    width: 80px;
    text-align: center;
}

.uf-financial-table .col-actions {
    width: 120px;
    text-align: center;
}

/* 财务按钮专用样式 */
.uf-financial-btn {
    font-family: var(--uf-font-family);
    font-size: 12px;
    font-weight: 500;
    padding: 4px 13px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    color: #333;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    text-decoration: none;
    transition: var(--uf-transition);
    min-height: 24px;
    box-shadow: var(--uf-box-shadow);
    vertical-align: middle;
    white-space: nowrap;
}

.uf-financial-btn:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
    text-decoration: none;
    box-shadow: var(--uf-box-shadow-hover);
}

.uf-financial-btn.generate {
    background: linear-gradient(to bottom, var(--uf-success) 0%, #1e7e34 100%);
    border-color: #1e7e34;
    color: white;
}

.uf-financial-btn.generate:hover {
    background: linear-gradient(to bottom, #28a745 0%, var(--uf-success) 100%);
    border-color: var(--uf-success);
    color: white;
}

.uf-financial-btn.approve {
    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
    border-color: var(--uf-primary-dark);
    color: white;
}

.uf-financial-btn.approve:hover {
    background: linear-gradient(to bottom, var(--uf-primary-light) 0%, var(--uf-primary) 100%);
    border-color: var(--uf-primary);
    color: white;
}

.uf-financial-btn.cancel {
    background: linear-gradient(to bottom, var(--uf-danger) 0%, #bd2130 100%);
    border-color: #bd2130;
    color: white;
}

.uf-financial-btn.cancel:hover {
    background: linear-gradient(to bottom, #dc3545 0%, var(--uf-danger) 100%);
    border-color: var(--uf-danger);
    color: white;
}

.uf-financial-btn.export {
    background: linear-gradient(to bottom, #f0ad4e 0%, #ec971f 100%);
    border-color: #ec971f;
    color: white;
}

.uf-financial-btn.export:hover {
    background: linear-gradient(to bottom, #f0ad4e 0%, #d58512 100%);
    border-color: #d58512;
    color: white;
}

.uf-financial-btn.import {
    background: linear-gradient(to bottom, #5bc0de 0%, #31b0d5 100%);
    border-color: #31b0d5;
    color: white;
}

.uf-financial-btn.import:hover {
    background: linear-gradient(to bottom, #5bc0de 0%, #269abc 100%);
    border-color: #269abc;
    color: white;
}

/* 财务表单专用样式 */
.uf-financial-form {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: 16px;
    box-shadow: var(--uf-box-shadow);
}

.uf-financial-form-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 16px;
}

.uf-financial-form-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.uf-financial-form-label {
    font-weight: 600;
    color: #333;
    font-size: 12px;
    min-width: 80px;
    text-align: right;
    white-space: nowrap;
}

.uf-financial-form-control {
    font-family: var(--uf-font-family);
    font-size: 12px;
    padding: 4px 6px;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: white;
    color: #333;
    min-height: 28px;
    line-height: 1.2;
}

.uf-financial-form-control:focus {
    border-color: var(--uf-primary);
    outline: none;
    box-shadow: 0 0 3px rgba(0, 102, 204, 0.3);
    background: var(--uf-input-focus);
}

.uf-financial-form-control:disabled {
    background: var(--uf-disabled-bg);
    color: var(--uf-disabled-text);
    cursor: not-allowed;
}

.uf-financial-form-control[readonly] {
    background: #f9f9f9;
    cursor: default;
}

/* 财务汇总信息样式 */
.uf-financial-summary-card {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border: 1px solid var(--uf-primary);
    border-radius: var(--uf-border-radius);
    padding: 12px;
    margin-bottom: 16px;
}

.uf-financial-summary-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--uf-primary);
    margin-bottom: 8px;
}

.uf-financial-summary-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.uf-financial-summary-item {
    text-align: center;
}

.uf-financial-summary-label {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
}

.uf-financial-summary-value {
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-size: 14px;
    font-weight: 600;
    color: var(--uf-primary);
}

/* 财务图标样式 */
.uf-financial-icon {
    font-size: 14px;
    color: var(--uf-primary);
    margin-right: 4px;
    vertical-align: middle;
}

.uf-financial-icon.success {
    color: var(--uf-success);
}

.uf-financial-icon.warning {
    color: var(--uf-warning);
}

.uf-financial-icon.danger {
    color: var(--uf-danger);
}

.uf-financial-icon.info {
    color: var(--uf-info);
}

/* 财务工具栏样式 */
.uf-financial-toolbar {
    background: var(--uf-toolbar-bg);
    border: 1px solid var(--uf-border);
    padding: 8px 12px;
    margin-bottom: 12px;
    border-radius: var(--uf-border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.uf-financial-toolbar-left,
.uf-financial-toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.uf-financial-toolbar-title {
    font-weight: 600;
    color: var(--uf-primary);
    font-size: 13px;
}

/* 财务分页样式 */
.uf-financial-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 12px;
    background: #f8f9fa;
    border-top: 1px solid var(--uf-grid-border);
    font-size: 12px;
}

.uf-financial-page-info {
    color: #666;
    margin: 0 16px;
    font-size: 12px;
}

/* 财务审批流程样式 */
.uf-approval-flow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    margin: 16px 0;
    border-top: 1px solid var(--uf-border);
    border-bottom: 1px solid var(--uf-border);
}

.uf-approval-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 100%;
}

.uf-approval-step:not(:last-child)::after {
    content: "";
    position: absolute;
    top: 15px;
    left: 50%;
    width: 100%;
    height: 2px;
    background: #e9ecef;
    z-index: 0;
}

.uf-approval-step.active:not(:last-child)::after {
    background: var(--uf-primary);
}

.uf-approval-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    z-index: 1;
}

.uf-approval-step.active .uf-approval-icon {
    background: var(--uf-primary);
    color: white;
}

.uf-approval-step.completed .uf-approval-icon {
    background: var(--uf-success);
    color: white;
}

.uf-approval-step.rejected .uf-approval-icon {
    background: var(--uf-danger);
    color: white;
}

.uf-approval-label {
    margin-top: 8px;
    font-size: 13px;
    text-align: center;
    color: #6c757d;
}

.uf-approval-step.active .uf-approval-label,
.uf-approval-step.completed .uf-approval-label {
    color: var(--uf-primary);
    font-weight: 500;
}

.uf-approval-step.rejected .uf-approval-label {
    color: var(--uf-danger);
    font-weight: 500;
}

/* 财务数字键盘样式 */
.uf-numeric-keyboard {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    padding: 8px;
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    box-shadow: var(--uf-box-shadow);
    width: 200px;
    position: absolute;
    z-index: 10;
}

.uf-numeric-key {
    padding: 8px;
    font-size: 14px;
    text-align: center;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    cursor: pointer;
    user-select: none;
}

.uf-numeric-key:hover {
    background: var(--uf-row-hover);
    border-color: var(--uf-primary);
}

.uf-numeric-key:active {
    background: var(--uf-selected);
}

.uf-numeric-key.zero {
    grid-column: span 2;
}

.uf-numeric-key.clear,
.uf-numeric-key.backspace {
    background: #e9ecef;
    color: #6c757d;
}

.uf-numeric-key.enter {
    background: var(--uf-primary);
    color: white;
    border-color: var(--uf-primary-dark);
}

/* 财务弹窗样式 */
.uf-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.uf-modal.show {
    opacity: 1;
    visibility: visible;
}

.uf-modal-content {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    width: 600px;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    transform: translateY(-20px);
    transition: transform 0.2s ease;
}

.uf-modal.show .uf-modal-content {
    transform: translateY(0);
}

.uf-modal-header {
    background: var(--uf-header-bg);
    border-bottom: 1px solid var(--uf-border);
    padding: 13px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.uf-modal-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-modal-close {
    font-size: 16px;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
}

.uf-modal-close:hover {
    color: #333;
}

.uf-modal-body {
    padding: 12px;
    overflow-y: auto;
    flex: 1;
}

.uf-modal-footer {
    background: #f8f9fa;
    border-top: 1px solid var(--uf-border);
    padding: 8px 12px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* 财务进度条样式 */
.uf-progress {
    height: 13px;
    background: #e9ecef;
    border-radius: var(--uf-border-radius);
    overflow: hidden;
    margin: 8px 0;
}

.uf-progress-bar {
    height: 100%;
    background: var(--uf-primary);
    transition: width 0.3s ease;
}

.uf-progress-bar.success {
    background: var(--uf-success);
}

.uf-progress-bar.warning {
    background: var(--uf-warning);
}

.uf-progress-bar.danger {
    background: var(--uf-danger);
}

/* 财务时间轴样式 */
.uf-timeline {
    position: relative;
    padding: 16px 0;
    margin: 16px 0;
}

.uf-timeline::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 15px;
    width: 2px;
    background: #e9ecef;
}

.uf-timeline-item {
    position: relative;
    margin-bottom: 16px;
    padding-left: 40px;
}

.uf-timeline-dot {
    position: absolute;
    left: 8px;
    top: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #e9ecef;
    border: 2px solid white;
    z-index: 1;
}

.uf-timeline-item.completed .uf-timeline-dot {
    background: var(--uf-success);
}

.uf-timeline-item.active .uf-timeline-dot {
    background: var(--uf-primary);
}

.uf-timeline-item.pending .uf-timeline-dot {
    background: #e9ecef;
}

.uf-timeline-content {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: 8px 12px;
}

.uf-timeline-title {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.uf-timeline-time {
    font-size: 13px;
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --uf-font-size: 11px;
        --uf-font-size-large: 12px;
        --uf-font-size-small: 13px;
        --uf-btn-height: 28px;
        --uf-btn-height-sm: 24px;
        --uf-btn-height-lg: 32px;
        --uf-form-label-width: 80px;
    }

    .uf-table th,
    .uf-table td {
        padding: 2px 4px;
        font-size: var(--uf-font-size-small);
    }

    .uf-btn {
        padding: 4px 8px;
        min-height: var(--uf-btn-height);
    }

    .uf-btn-group {
        flex-direction: column;
        gap: 1px;
    }

    .uf-btn-group .uf-btn {
        border-radius: var(--uf-border-radius);
    }

    .uf-form-control {
        padding: 4px 6px;
    }

    .uf-card-body {
        padding: 8px;
    }

    .uf-financial-form-row {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .uf-financial-form-group {
        flex-direction: column;
        align-items: stretch;
        gap: 4px;
    }

    .uf-financial-form-label {
        text-align: left;
        min-width: auto;
    }

    .uf-financial-summary-content {
        flex-direction: column;
        gap: 8px;
    }

    .uf-financial-toolbar {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .uf-financial-toolbar-left,
    .uf-financial-toolbar-right {
        justify-content: center;
    }
    
    .uf-modal-content {
        width: 90%;
    }
    
    .uf-approval-flow {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .uf-approval-step {
        flex-direction: row;
        align-items: center;
        gap: 8px;
        width: auto;
    }
    
    .uf-approval-step:not(:last-child)::after {
        top: 50%;
        left: 30px;
        width: 2px;
        height: calc(100% + 16px);
    }
    
    .uf-approval-label {
        margin-top: 0;
        text-align: left;
    }
}

/* 用友表格容器样式 */
.uf-table-container {
    overflow-x: auto;
    border: 1px solid var(--uf-grid-border);
    border-radius: var(--uf-border-radius);
    background: white;
}

/* 用友二级按钮样式 */
.uf-btn-secondary {
    background: linear-gradient(to bottom, #6c757d 0%, #5a6268 100%);
    border-color: #5a6268;
    color: white;
    font-weight: 500;
}

.uf-btn-secondary:hover {
    background: linear-gradient(to bottom, #5a6268 0%, #545b62 100%);
    border-color: #545b62;
    color: white;
}

.uf-btn-secondary:active {
    background: #545b62;
}

/* 用友图标样式 */
.uf-icon {
    margin-right: 3px;
    font-size: inherit;
    vertical-align: middle;
}

.uf-icon-only {
    margin-right: 0;
}

.uf-icon-left {
    margin-right: 4px;
    margin-left: 0;
}

.uf-icon-right {
    margin-left: 4px;
    margin-right: 0;
}

/* ========================================
   用友布局和工具类样式
   ======================================== */

/* 统一容器布局 */
.uf-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

/* 统一网格布局系统 */
.uf-grid {
    display: grid;
    grid-gap: var(--uf-grid-gap);
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
}

/* 响应式列布局 - 替代原有col-md类 */
.uf-grid-col-1 { grid-column: span 1; }
.uf-grid-col-2 { grid-column: span 2; }
.uf-grid-col-3 { grid-column: span 3; }
.uf-grid-col-4 { grid-column: span 4; }
.uf-grid-col-6 { grid-column: span 6; }
.uf-grid-col-12 { grid-column: span 12; }

@media (max-width: 768px) {
    .uf-grid {
        grid-template-columns: 1fr;
        grid-gap: 8px;
    }
}

/* 兼容老布局类，逐步废弃 */
.uf-row {
    display: block;
}
.uf-col-md-3,
.uf-col-md-4,
.uf-col-md-6,
.uf-col-md-12 {
    display: none;
}

/* 用友统计卡片 */
.uf-stat-card {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: var(--uf-box-shadow);
    transition: var(--uf-transition);
    height: 100%;
}

.uf-stat-card:hover {
    border-color: var(--uf-primary);
    box-shadow: var(--uf-box-shadow-hover);
}

.uf-stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--uf-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
}

.uf-stat-content {
    flex: 1;
    min-width: 0;
}

.uf-stat-title {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
}

.uf-stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    font-family: 'Times New Roman', var(--uf-font-family);
}

/* 用友文本对齐工具类 */
.uf-text-left { text-align: left !important; }
.uf-text-center { text-align: center !important; }
.uf-text-right { text-align: right !important; }
.uf-text-muted { color: #666 !important; }

/* 用友卡片工具栏 */
.uf-card-tools {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

/* 用友表单布局 */
.uf-form {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: flex-end;
}

.uf-form .uf-form-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 0;
}

.uf-form .uf-form-label {
    margin-bottom: 2px;
    margin-right: 0;
    text-align: left;
    min-width: auto;
}

/* 用友按钮组 */
.uf-btn-group {
    display: inline-flex;
    gap: 2px;
}

.uf-btn-group .uf-btn {
    border-radius: 0;
}

.uf-btn-group .uf-btn:first-child {
    border-radius: var(--uf-border-radius) 0 0 var(--uf-border-radius);
}

.uf-btn-group .uf-btn:last-child {
    border-radius: 0 var(--uf-border-radius) var(--uf-border-radius) 0;
}

.uf-btn-group .uf-btn:only-child {
    border-radius: var(--uf-border-radius);
}

/* 用友响应式工具类 */
@media (max-width: 768px) {
    .uf-col-md-3,
    .uf-col-md-4,
    .uf-col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .uf-row {
        margin: -2px;
    }

    .uf-col-md-3,
    .uf-col-md-4,
    .uf-col-md-6,
    .uf-col-md-12 {
        padding: 2px;
    }

    .uf-stat-card {
        padding: 8px;
        gap: 8px;
    }

    .uf-stat-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .uf-stat-value {
        font-size: 14px;
    }
}

/* ========================================
   科目余额表专用样式
   ======================================== */

/* 科目余额表容器 */
.uf-balance-sheet-container {
    display: flex;
    flex-direction: column;
    gap: 13px;
}

/* 科目余额表表格样式 */
.uf-balance-sheet-table {
    font-family: 'Microsoft YaHei', 'SimSun', sans-serif;
    font-size: 13px;
    border-collapse: collapse;
}

.uf-balance-sheet-table th {
    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%) !important;
    color: white !important;
    text-align: center;
    vertical-align: middle;
    font-weight: 600;
    font-size: 13px;
    border: 1px solid var(--uf-grid-border);
    padding: 4px 6px;
    height: 24px;
    cursor: pointer;
}

.uf-balance-sheet-table th.sortable:hover {
    background: linear-gradient(to bottom, var(--uf-primary-dark) 0%, var(--uf-primary) 100%) !important;
}

.uf-balance-sheet-table th.sort-asc::after {
    content: ' ↑';
    color: #fff;
}

.uf-balance-sheet-table th.sort-desc::after {
    content: ' ↓';
    color: #fff;
}

.uf-balance-sheet-table td {
    vertical-align: middle;
    padding: 3px 6px;
    font-size: 13px;
    border: 1px solid var(--uf-grid-border);
    height: 22px;
    line-height: 1.2;
}

.uf-balance-sheet-table .uf-level-1-row {
    background: #f8f9fa;
    font-weight: 600;
}

.uf-balance-sheet-table .uf-level-1-row:hover {
    background: var(--uf-row-hover) !important;
}

/* 科目层级样式 */
.uf-subject-code-cell,
.uf-subject-name-cell {
    position: relative;
}

.uf-level-indent {
    display: inline-block;
}

.uf-level-1-name {
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-level-badge {
    display: inline-block;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 13px;
    font-weight: 600;
    text-align: center;
    min-width: 16px;
}

.uf-level-1 {
    background: var(--uf-primary);
    color: white;
}

.uf-level-2 {
    background: var(--uf-info);
    color: white;
}

.uf-level-3 {
    background: var(--uf-success);
    color: white;
}

.uf-level-4 {
    background: var(--uf-warning);
    color: white;
}

.uf-level-5 {
    background: var(--uf-danger);
    color: white;
}

/* 余额金额样式 */
.uf-balance-positive {
    color: var(--uf-success);
}

.uf-balance-negative {
    color: var(--uf-danger);
}

/* 合计行样式 */
.uf-total-row {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%) !important;
    font-weight: 600;
    border-top: 2px solid var(--uf-primary) !important;
}

.uf-total-row th {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%) !important;
    color: var(--uf-primary) !important;
    border: 1px solid var(--uf-primary) !important;
}

.uf-amount-total {
    font-weight: 700;
    color: var(--uf-primary);
}

/* 分析网格布局 */
.uf-analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 13px;
    margin-top: 13px;
}

/* 统计网格布局 */
.uf-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.uf-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-stat-label {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
}

.uf-stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-amount-large {
    font-size: 16px;
    font-weight: 700;
}

/* 类型统计样式 */
.uf-type-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.uf-type-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-type-name {
    font-weight: 600;
}

.uf-type-details {
    font-size: 13px;
    color: #666;
}

.uf-count-badge {
    display: inline-block;
    padding: 1px 4px;
    background: var(--uf-primary);
    color: white;
    border-radius: 2px;
    font-size: 13px;
    font-weight: 600;
    min-width: 16px;
    text-align: center;
}

/* 信息卡片样式 */
.uf-info-card {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
}

.uf-info-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.uf-info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #333;
}

.uf-info-icon {
    color: var(--uf-success);
    font-size: 12px;
    flex-shrink: 0;
}

/* 查询表单样式 */
.uf-search-form {
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    padding: 12px;
    margin-bottom: 13px;
    border-radius: var(--uf-border-radius);
}

.uf-query-form {
    margin: 0;
}

.uf-record-count {
    font-size: 13px;
    color: #666;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 2px;
    border: 1px solid var(--uf-border);
}

/* 科目余额表响应式设计 */
@media (max-width: 768px) {
    .uf-analysis-grid {
        grid-template-columns: 1fr;
    }

    .uf-stats-grid {
        grid-template-columns: 1fr;
    }

    .uf-balance-sheet-table {
        font-size: 13px;
    }

    .uf-balance-sheet-table th,
    .uf-balance-sheet-table td {
        padding: 2px 4px;
        font-size: 13px;
    }

    .uf-type-stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}

/* 打印样式优化 */
@media print {
    .uf-table,
    .uf-financial-table {
        font-size: 12px;
    }
    
    .uf-table th,
    .uf-table td,
    .uf-financial-table th,
    .uf-financial-table td {
        font-size: 12px;
        padding: 4px 6px;
    }
}

/* 现代UI效果增强 */
.uf-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--uf-gray-200);
    border-top-color: var(--uf-primary);
    border-radius: 50%;
    animation: uf-spin 0.8s linear infinite;
}
@keyframes uf-spin {
    to { transform: rotate(360deg); }
}
.uf-divider {
    height: 1px;
    background: linear-gradient(to right, transparent, var(--uf-gray-200), transparent);
    margin: 16px 0;
}
.uf-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
    color: var(--uf-white);
}
.uf-badge-primary { background: var(--uf-primary); }
.uf-badge-success { background: var(--uf-success); }
.uf-badge-warning { background: var(--uf-warning); }
.uf-badge-danger { background: var(--uf-danger); }

/* ========================================
   财务凭证编辑专用样式 (整合自 voucher-edit.css)
   ======================================== */

/* 纸质凭证极致还原风格增强 */
.voucher-edit-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: var(--uf-font-family);
}

.voucher-header-section {
    background: #fff;
    border: 2px solid #000;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: var(--uf-box-shadow);
}

.voucher-header-title {
    text-align: center;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.voucher-header-form .form-control-sm {
    display: inline-block;
    margin: 0 5px;
    border: 1px solid #000;
}

.voucher-header-form .form-label {
    font-weight: bold;
    margin-right: 5px;
}

.voucher-table-section {
    background: #fff;
    border: 2px solid #000;
    padding: 0;
    margin-bottom: 20px;
    box-shadow: var(--uf-box-shadow);
}

.voucher-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-size: var(--uf-font-size-large);
}

.voucher-table th,
.voucher-table td {
    border: 1px solid #000;
    padding: 8px;
    text-align: center;
    vertical-align: middle;
}

.voucher-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
    position: relative;
}

.amount-units {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    padding: 0 5px;
    font-size: 13px;
    font-weight: normal;
    color: #666;
    border-top: 1px solid #ddd;
    padding-top: 3px;
}

.amount-units span {
    flex: 1;
    text-align: center;
    padding: 1px 2px;
    background-color: rgba(255, 255, 255, 0.8);
    border-right: 1px solid #eee;
}

.amount-units span:last-child {
    border-right: none;
}

.voucher-table .form-control {
    border: none;
    background: transparent;
    text-align: center;
    padding: 4px;
    font-size: var(--uf-font-size-large);
    width: 100%;
    box-shadow: none;
}

.voucher-table .form-control:focus {
    outline: none;
    box-shadow: inset 0 0 3px var(--uf-primary);
}

.voucher-table .amount-input {
    text-align: right;
    background-image:
        linear-gradient(to right, transparent 0px, transparent calc(10% - 0.5px), #b0b0b0 calc(10% - 0.5px), #b0b0b0 calc(10% + 0.5px), transparent calc(10% + 0.5px)),
        linear-gradient(to right, transparent 0px, transparent calc(20% - 0.5px), #b0b0b0 calc(20% - 0.5px), #b0b0b0 calc(20% + 0.5px), transparent calc(20% + 0.5px)),
        linear-gradient(to right, transparent 0px, transparent calc(30% - 0.5px), #b0b0b0 calc(30% - 0.5px), #b0b0b0 calc(30% + 0.5px), transparent calc(30% + 0.5px)),
        linear-gradient(to right, transparent 0px, transparent calc(40% - 0.5px), #c0c0c0 calc(40% - 0.5px), #c0c0c0 calc(40% + 0.5px), transparent calc(40% + 0.5px)),
        linear-gradient(to right, transparent 0px, transparent calc(50% - 0.5px), #b0b0b0 calc(50% - 0.5px), #b0b0b0 calc(50% + 0.5px), transparent calc(50% + 0.5px)),
        linear-gradient(to right, transparent 0px, transparent calc(60% - 0.5px), #b0b0b0 calc(60% - 0.5px), #b0b0b0 calc(60% + 0.5px), transparent calc(60% + 0.5px)),
        linear-gradient(to right, transparent 0px, transparent calc(70% - 0.5px), #b0b0b0 calc(70% - 0.5px), #b0b0b0 calc(70% + 0.5px), transparent calc(70% + 0.5px)),
        linear-gradient(to right, transparent 0px, transparent calc(80% - 1px), #808080 calc(80% - 1px), #808080 calc(80% + 1px), transparent calc(80% + 1px)),
        linear-gradient(to right, transparent 0px, transparent calc(90% - 0.5px), #b0b0b0 calc(90% - 0.5px), #b0b0b0 calc(90% + 0.5px), transparent calc(90% + 0.5px));
    background-color: #fafafa;
    font-family: 'Consolas', 'Courier New', 'SimSun', monospace;
    font-weight: bold;
    font-size: var(--uf-font-size-large);
    padding: 8px 3px;
    letter-spacing: 0px;
    min-height: 40px;
    border-radius: 0;
    color: #333;
    width: 100%;
}

.voucher-table .amount-input:focus {
    background-color: rgba(255, 255, 240, 0.8);
    box-shadow: inset 0 0 8px rgba(0, 123, 255, 0.2);
    outline: none;
}

.voucher-table .amount-input:not(:placeholder-shown) {
    background-color: #fff;
    color: #000;
    font-weight: 600;
}

.voucher-table .amount-input::placeholder {
    color: transparent;
    font-size: var(--uf-font-size);
}

.voucher-table .amount-input {
    direction: ltr;
    unicode-bidi: bidi-override;
}

.total-row {
    background-color: #f8f9fa;
    font-weight: bold;
    border-top: 2px solid #000;
}

.total-row td {
    font-size: var(--uf-font-size);
    color: #333;
}

.amount-words-row {
    background-color: #fff;
    border-top: 1px solid #000;
}

.amount-words-row td {
    text-align: left;
    padding: 12px;
    font-weight: bold;
    font-size: var(--uf-font-size);
    color: #333;
}

.voucher-actions {
    padding: 15px;
    text-align: center;
    border-top: 1px solid #000;
    background-color: #f8f9fa;
}

.voucher-actions .btn {
    margin: 0 10px;
    min-width: 120px;
}

.voucher-signature-section {
    background: #fff;
    border: 2px solid #000;
    padding: 15px;
    box-shadow: var(--uf-box-shadow);
}

.signature-row {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.signature-item {
    display: flex;
    align-items: center;
    flex-direction: column;
}

.signature-label {
    margin-bottom: 5px;
    font-weight: bold;
    font-size: var(--uf-font-size);
}

.signature-box {
    display: inline-block;
    width: 80px;
    height: 30px;
    border-bottom: 2px solid #000;
    text-align: center;
    line-height: 30px;
    font-size: var(--uf-font-size);
}

.voucher-table .btn-group-sm {
    display: flex;
    gap: 2px;
}

.voucher-table .btn-group-sm .btn {
    flex: 1;
    padding: 0.15rem 0.3rem;
}

/* 科目选择弹窗样式 */
.subject-tree {
    max-height: 400px;
    overflow-y: auto;
}

.subject-group-title {
    cursor: pointer;
    user-select: none;
}

.subject-group-title:hover {
    background-color: #f8f9fa;
}

.subject-item .d-flex {
    cursor: pointer;
    transition: background-color 0.2s;
}

.subject-item .d-flex:hover {
    background-color: #e9ecef;
}

.subject-item .d-flex.selected {
    background-color: var(--uf-primary) !important;
    color: white !important;
}

.subject-item .d-flex.selected code {
    background: none !important;
    color: white !important;
}

.expand-icon {
    cursor: pointer;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.expand-icon:hover {
    background-color: rgba(0,0,0,0.1);
    border-radius: 2px;
}

.hover-bg-light:hover {
    background-color: #f8f9fa;
}

/* 搜索框样式 */
#subject-search {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
}

#subject-search:focus {
    border-color: var(--uf-primary);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* ========================================
   专业记账凭证编辑器样式 (整合自 voucher-editor.css)
   ======================================== */

.voucher-editor-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    box-shadow: var(--uf-box-shadow-hover);
}

/* 编辑器表格样式 */
.voucher-editor-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border: 2px solid #333;
    font-family: 'Courier New', monospace;
    font-size: var(--uf-font-size-large);
}

.voucher-editor-table th {
    background: #e9ecef;
    border: 1px solid #333;
    padding: 8px;
    text-align: center;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.voucher-editor-table td {
    border: 1px solid #333;
    padding: 0;
    position: relative;
}

/* 可编辑单元格 */
.editable-cell {
    min-height: 35px;
    padding: 6px 8px;
    border: none;
    outline: none;
    width: 100%;
    background: transparent;
    font-family: inherit;
    font-size: inherit;
    resize: none;
    overflow: hidden;
}

.editable-cell:focus {
    background: #fff3cd;
    box-shadow: inset 0 0 0 2px var(--uf-primary);
}

/* 选中的行 */
.voucher-editor-table tr.selected {
    background: #e3f2fd;
}

.voucher-editor-table tr.active {
    background: #fff3cd;
}

/* 科目选择器 */
.subject-selector {
    position: relative;
    width: 100%;
}

.subject-input {
    width: 100%;
    padding: 6px 8px;
    border: none;
    outline: none;
    background: transparent;
    font-family: inherit;
    cursor: pointer;
}

.subject-input:focus {
    background: #fff3cd;
    box-shadow: inset 0 0 0 2px var(--uf-primary);
}

.subject-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ccc;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.subject-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.subject-option:hover,
.subject-option.highlighted {
    background: var(--uf-primary);
    color: white;
}

.subject-option .subject-code {
    font-weight: bold;
    color: #666;
}

.subject-option .subject-name {
    margin-left: 8px;
}

/* 金额输入框 */
.amount-cell {
    text-align: right;
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.amount-input {
    text-align: right;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    letter-spacing: 1px;
}

.amount-input.debit {
    color: var(--uf-danger);
}

.amount-input.credit {
    color: var(--uf-success);
}

/* 合计行 */
.total-row {
    background: #f8f9fa;
    font-weight: bold;
}

.total-row td {
    border-top: 2px solid #333;
}

.amount-words-row {
    background: #f8f9fa;
}

.amount-words-row td {
    padding: 8px;
    font-weight: bold;
    color: #495057;
}

/* 操作按钮 */
.row-actions {
    text-align: center;
    padding: 4px;
}

.row-actions .btn {
    margin: 0 2px;
    padding: 2px 6px;
    font-size: 12px;
}

/* 工具栏 */
.voucher-toolbar {
    background: white;
    padding: 10px;
    border: 1px solid #ddd;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
}

.voucher-toolbar .btn {
    margin-right: 8px;
    margin-bottom: 4px;
}

/* 状态指示器 */
.balance-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    margin-left: 10px;
}

.balance-indicator.balanced {
    background: #d4edda;
    color: #155724;
}

.balance-indicator.unbalanced {
    background: #f8d7da;
    color: #721c24;
}

/* 快捷键提示 */
.shortcut-hint {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    display: none;
}

.shortcut-hint.show {
    display: block;
}

/* 行号 */
.row-number {
    background: #e9ecef;
    text-align: center;
    font-weight: bold;
    color: #666;
    width: 40px;
    user-select: none;
}

/* 错误提示 */
.cell-error {
    border: 2px solid var(--uf-danger) !important;
    background: #f8d7da !important;
}

.error-tooltip {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--uf-danger);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1001;
    white-space: nowrap;
}

/* 自动完成 */
.autocomplete-container {
    position: relative;
}

.autocomplete-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ccc;
    max-height: 150px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.autocomplete-suggestion {
    padding: 6px 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.autocomplete-suggestion:hover,
.autocomplete-suggestion.selected {
    background: var(--uf-primary);
    color: white;
}

/* ========================================
   财务打印样式 (整合自 print-styles.css)
   ======================================== */

/* 通用打印样式 */
@media print {
    /* 重置页面样式 */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* 隐藏不需要打印的元素 */
    .no-print,
    .navbar,
    .sidebar,
    .btn,
    .pagination,
    .breadcrumb,
    .alert,
    .modal,
    .tooltip,
    .popover,
    .voucher-actions,
    .btn-group-sm,
    .financial-actions,
    .voucher-toolbar,
    .row-actions {
        display: none !important;
    }

    /* 页面设置 */
    @page {
        margin: 15mm;
        size: A4;
    }

    /* A4横向打印 */
    @page landscape {
        size: A4 landscape;
        margin: 10mm 15mm;
    }

    /* 基础样式 */
    body {
        font-family: 'SimSun', '宋体', serif !important;
        font-size: 12px !important;
        line-height: 1.4 !important;
        color: #000 !important;
        background: white !important;
    }

    /* 标题样式 */
    .print-title {
        font-size: 18px !important;
        font-weight: bold !important;
        text-align: center !important;
        margin: 0 0 20px 0 !important;
        padding: 0 !important;
        border: none !important;
    }

    .print-subtitle {
        font-size: 14px !important;
        font-weight: bold !important;
        text-align: center !important;
        margin: 0 0 15px 0 !important;
    }

    /* 表格样式 */
    .print-table,
    .voucher-table,
    .voucher-editor-table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 10px 0 !important;
        font-size: 13px !important;
    }

    .print-table th,
    .print-table td,
    .voucher-table th,
    .voucher-table td,
    .voucher-editor-table th,
    .voucher-editor-table td {
        border: 1px solid #000 !important;
        padding: 4px 6px !important;
        text-align: left !important;
        vertical-align: middle !important;
        background: white !important;
    }

    .print-table th,
    .voucher-table th,
    .voucher-editor-table th {
        background: #f0f0f0 !important;
        font-weight: bold !important;
        text-align: center !important;
    }

    .print-table .amount,
    .voucher-table .amount-input,
    .amount-cell {
        text-align: right !important;
        font-family: 'Arial', sans-serif !important;
    }

    .print-table .center {
        text-align: center !important;
    }

    /* 凭证专用样式 */
    .voucher-print {
        page: landscape;
    }

    .voucher-edit-container {
        box-shadow: none;
        border: 3px solid #000;
        max-width: none;
        margin: 0;
        padding: 0;
    }

    .voucher-header-section,
    .voucher-table-section,
    .voucher-signature-section {
        border: none;
        box-shadow: none;
        page-break-inside: avoid;
        background: #fff !important;
    }

    .voucher-table tfoot .total-row td {
        background: #fff !important;
    }

    .voucher-table tfoot .amount-words-row td {
        background: #fff !important;
    }

    .signature-box {
        min-width: 160px;
        height: 32px;
    }

    .editable-cell {
        border: none;
        background: transparent !important;
    }

    /* 金额格式化 */
    .amount-cell {
        text-align: right !important;
        font-family: 'Arial', 'Times New Roman', monospace !important;
        white-space: nowrap !important;
    }

    /* 科目编码格式化 */
    .subject-code {
        font-family: 'Arial', 'Times New Roman', monospace !important;
        font-weight: bold !important;
    }

    /* 隐藏背景色和阴影 */
    .card,
    .table-striped tbody tr:nth-of-type(odd),
    .table-hover tbody tr:hover {
        background: white !important;
        box-shadow: none !important;
    }

    /* 强制显示边框 */
    .table,
    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}

/* 屏幕预览样式 */
.print-preview {
    background: white;
    padding: 20px;
    margin: 20px auto;
    max-width: 210mm;
    min-height: 297mm;
    box-shadow: var(--uf-box-shadow-hover);
    font-family: 'SimSun', '宋体', serif;
    font-size: 12px;
    line-height: 1.4;
}

.print-preview.landscape {
    max-width: 297mm;
    min-height: 210mm;
}

/* 打印按钮样式 */
.print-controls {
    text-align: center;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.print-btn {
    background: var(--uf-primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin: 0 5px;
    font-size: var(--uf-font-size-large);
}

.print-btn:hover {
    background: var(--uf-primary-dark);
}

.print-btn.landscape {
    background: var(--uf-success);
}

.print-btn.landscape:hover {
    background: #1e7e34;
}

/* ========================================
   财务模块响应式设计增强
   ======================================== */

/* 移动端凭证编辑优化 */
@media (max-width: 768px) {
    .voucher-edit-container {
        padding: 10px;
    }

    .voucher-header-form .row {
        flex-direction: column;
    }

    .voucher-header-form .col-auto {
        margin-bottom: 10px;
        width: 100%;
    }

    .voucher-header-form .form-control-sm {
        width: 100%;
        margin: 5px 0;
    }

    .signature-row {
        flex-direction: column;
        gap: 15px;
    }

    .signature-item {
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
    }

    .voucher-table {
        font-size: 12px;
    }

    .voucher-table th,
    .voucher-table td {
        padding: 4px;
    }

    .voucher-actions .btn {
        margin: 5px;
        min-width: 100px;
    }

    .voucher-editor-table {
        font-size: 12px;
    }

    .editable-cell {
        padding: 4px 6px;
        min-height: 30px;
    }

    .voucher-toolbar .btn {
        padding: 4px 8px;
        font-size: 12px;
    }

    .print-preview {
        max-width: 100%;
        margin: 10px;
        padding: 15px;
    }

    .print-controls {
        margin: 10px 0;
        padding: 10px;
    }

    .print-btn {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
}

/* 财务模块全局响应式优化 */
@media (max-width: 768px) {
    .uf-financial-content {
        padding: 8px;
        font-size: 12px;
    }

    .uf-card-body {
        padding: 8px;
    }

    .uf-form-row {
        flex-direction: column;
        gap: 8px;
    }

    .uf-form-group {
        min-width: auto;
        width: 100%;
    }

    .uf-btn-group {
        flex-wrap: wrap;
    }

    .uf-table {
        font-size: 13px;
    }

    .uf-table th,
    .uf-table td {
        padding: 4px 6px;
    }

    .uf-btn {
        font-size: 13px;
        padding: 0 12px;
        min-height: 28px;
    }

    .uf-btn-sm {
        font-size: 13px;
        padding: 0 8px;
        min-height: 24px;
    }

    /* 财务报表移动端优化 */
    .uf-analysis-grid {
        grid-template-columns: 1fr;
    }

    .uf-stats-grid {
        grid-template-columns: 1fr;
    }

    .uf-balance-sheet-table {
        font-size: 13px;
    }

    .uf-balance-sheet-table th,
    .uf-balance-sheet-table td {
        padding: 2px 4px;
        font-size: 13px;
    }

    .uf-type-stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}

/* ========================================
   付款记录页面专用样式
   ======================================== */

/* 确保付款记录表格字体为13px */
.uf-table tbody td {
    font-size: 13px !important;
}

.uf-table tfoot td {
    font-size: 13px !important;
}

/* ========================================
   科目余额表页面专用样式
   ======================================== */

/* 确保科目余额表格字体为13px */
.uf-balance-sheet-table {
    font-size: 13px !important;
}

.uf-balance-sheet-table th,
.uf-balance-sheet-table td {
    font-size: 13px !important;
}

/* ========================================
   财务凭证列表页面样式
   ======================================== */

/* 凭证列表容器 */
.uf-voucher-list-container {
    background: var(--uf-light);
    min-height: 100vh;
    padding: 8px;
    overflow-x: auto;
}

.uf-voucher-list-window {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
    width: 100%;
    max-width: none;
    min-width: 1200px;
}

/* 凭证列表头部 */
.uf-voucher-list-header {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);
    border-bottom: 1px solid var(--uf-border);
    padding: 8px 15px;
    font-size: 13px;
    font-weight: 600;
    color: var(--uf-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.uf-window-controls {
    display: flex;
    gap: 4px;
}

/* 凭证列表工具栏 - 用友经典设计 */
.uf-voucher-list-toolbar {
    background: #F2F4F6;
    border-bottom: 1px solid #DDDDDD;
    padding: 6px 12px;
    display: flex;
    gap: 4px;
    align-items: center;
    flex-wrap: wrap;
    min-height: 40px;
}

.uf-toolbar-spacer {
    margin-left: auto;
}

.uf-batch-review-btn {
    display: none;
}

/* 用友经典工具栏按钮样式 */
.uf-voucher-list-toolbar .uf-btn {
    height: 32px;
    line-height: 30px;
    padding: 0 12px;
    font-size: 12px;
    border-radius: 3px;
    border: 1px solid #DDDDDD;
    background: linear-gradient(to bottom, #FFFFFF, #F5F5F5);
    color: #333333;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    user-select: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    margin: 0 2px;
}

.uf-voucher-list-toolbar .uf-btn:hover {
    background: linear-gradient(to bottom, #8bdeea, #3FC8DD);
    color: #FFFFFF;
    border-color: #3FC8DD;
    text-decoration: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.uf-voucher-list-toolbar .uf-btn:active {
    background: linear-gradient(to bottom, #33A4B5, #2A8A9A);
    border-color: #33A4B5;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.uf-voucher-list-toolbar .uf-btn-primary {
    background: linear-gradient(to bottom, #3FC8DD, #33A4B5);
    color: #FFFFFF;
    border-color: #3FC8DD;
    font-weight: 600;
}

.uf-voucher-list-toolbar .uf-btn-primary:hover {
    background: linear-gradient(to bottom, #57B8CB, #33A4B5);
    border-color: #33A4B5;
}

.uf-voucher-list-toolbar .uf-btn:disabled {
    background: #F5F5F5;
    color: #BBBBBB;
    border-color: #DDDDDD;
    cursor: not-allowed;
    box-shadow: none;
}

/* 圆形图标按钮 */
.uf-circle-btn {
    width: 24px;
    height: 24px;
    border: 1px solid #DDDDDD;
    border-radius: 50%;
    background: #FFFFFF;
    color: #666666;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 12px;
}

.uf-circle-btn:hover {
    background: #8bdeea;
    border-color: #3FC8DD;
    color: #FFFFFF;
}

.uf-circle-btn:active {
    background: #33A4B5;
    border-color: #33A4B5;
}

/* 方形按钮 */
.uf-square-btn {
    height: 32px;
    line-height: 32px;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    background: #FFFFFF;
    padding: 0 12px;
    color: #333333;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    font-size: 12px;
}

.uf-square-btn:hover {
    background: #3FC8DD;
    color: #FFFFFF;
    border-color: #3FC8DD;
}

.uf-square-btn:active {
    background: #33A4B5;
    border-color: #33A4B5;
}

/* 纯图标按钮 */
.uf-icon-btn {
    width: 24px;
    height: 24px;
    background: transparent;
    border: none;
    color: #666666;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 14px;
    border-radius: 3px;
}

.uf-icon-btn:hover {
    background: #8bdeea;
    color: #FFFFFF;
}

.uf-icon-btn:active {
    background: #33A4B5;
}

/* 凭证统计栏 */
.uf-voucher-stats-bar {
    background: #e8f4fd;
    border-bottom: 1px solid var(--uf-border);
    padding: 8px 15px;
    display: flex;
    gap: 24px;
    align-items: center;
    font-size: 13px;
}

.uf-stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.uf-stat-label {
    color: #666;
    font-size: 13px;
}

.uf-stat-value {
    font-weight: 600;
    color: var(--uf-primary);
    font-size: 13px;
}

.uf-stat-warning {
    color: #ff8f00 !important;
}

.uf-stat-success {
    color: #2e7d32 !important;
}

/* 凭证搜索栏 - 用友经典设计 */
.uf-voucher-search-bar {
    background: #F2F4F6;
    border-bottom: 1px solid #DDDDDD;
    padding: 8px 15px;
    min-height: 40px;
}

.uf-search-form {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    margin: 0;
}

.uf-search-group {
    display: flex;
    align-items: center;
    gap: 6px;
}

.uf-search-label {
    color: #333333;
    font-weight: normal;
    white-space: nowrap;
    font-size: 12px;
    min-width: 40px;
}

.uf-search-input {
    width: 120px;
    height: 24px;
    padding: 2px 6px;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    font-size: 12px;
    background: #FFFFFF;
}

.uf-search-input:focus {
    border-color: #3FC8DD;
    outline: none;
    box-shadow: 0 0 0 1px rgba(63, 200, 221, 0.3);
}

.uf-search-select {
    width: 80px;
    height: 24px;
    padding: 2px 6px;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    font-size: 12px;
    background: #FFFFFF;
}

.uf-search-select:focus {
    border-color: #3FC8DD;
    outline: none;
}

.uf-search-date {
    width: 110px;
    height: 24px;
    padding: 2px 6px;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    font-size: 12px;
    background: #FFFFFF;
}

.uf-search-date:focus {
    border-color: #3FC8DD;
    outline: none;
}

/* 凭证表格样式 - 用友经典设计 */
/* 表格容器 */
.uf-card-body {
    overflow-x: auto;
    padding: 0;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

/* 表格滚动条样式 */
.uf-card-body::-webkit-scrollbar {
    height: 8px;
}

.uf-card-body::-webkit-scrollbar-track {
    background: #F5F5F5;
    border-radius: 4px;
}

.uf-card-body::-webkit-scrollbar-thumb {
    background: #CCCCCC;
    border-radius: 4px;
}

.uf-card-body::-webkit-scrollbar-thumb:hover {
    background: #999999;
}

.uf-voucher-list-table {
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    font-size: 12px !important;
    table-layout: fixed;
    width: 1200px; /* 固定宽度确保所有列都能正常显示 */
    min-width: 1200px;
    border-collapse: collapse;
    border-spacing: 0;
}

.uf-voucher-list-table th,
.uf-voucher-list-table td {
    font-size: 12px !important;
    height: 32px;
    line-height: 32px;
    border: 1px solid #e2e2e2;
    padding: 0 4px;
    vertical-align: middle;
}

.uf-voucher-list-table th {
    background: #3FC8DD;
    color: #FFFFFF;
    font-weight: 600;
    text-align: center;
    border-color: #3FC8DD;
}

.uf-voucher-list-table tbody tr {
    background-color: #FFFFFF;
}

.uf-voucher-list-table tbody tr:nth-child(even) {
    background-color: rgba(63, 200, 221, 0.05);
}

.uf-voucher-list-table tbody tr:hover {
    background-color: #FFDEC9 !important;
}

.uf-voucher-list-table tbody tr.selected {
    background-color: #FFC6A2 !important;
}

.uf-voucher-list-table tbody tr.focused {
    background-color: #FFDEC9 !important;
}

/* 表格列宽 - 用友经典比例优化 */
.uf-checkbox-col {
    width: 50px;
    text-align: center;
    padding: 0 4px;
}
.uf-voucher-number-col {
    width: 180px;
    text-align: left;
}
.uf-date-col {
    width: 90px;
    text-align: center;
}
.uf-type-col {
    width: 90px;
    text-align: center;
}
.uf-summary-col {
    width: 350px;
    text-align: left;
}
.uf-amount-col {
    width: 140px;
    text-align: right;
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;
}
.uf-status-col {
    width: 90px;
    text-align: center;
}
.uf-action-col {
    width: 200px;
    text-align: center;
}

/* 表格单元格样式 - 用友经典设计 */
.uf-voucher-number-cell {
    padding: 2px 4px;
    vertical-align: middle;
}

.uf-voucher-number-link {
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    color: #000000;
    text-decoration: none;
    display: block;
    line-height: 1.4;
    word-break: break-all;
    white-space: normal;
}

.uf-voucher-number-link:hover {
    color: #3FC8DD;
    text-decoration: underline;
}

.uf-attachment-icon {
    font-size: 10px;
    color: #999999;
    margin-left: 2px;
}

.uf-date-cell {
    color: #333333;
    font-size: 12px;
    padding: 2px 4px;
}

.uf-type-cell {
    color: #333333;
    font-size: 12px;
    white-space: nowrap;
    padding: 2px 4px;
}

.uf-summary-cell {
    padding: 2px 4px;
}

.uf-summary-text {
    max-width: 340px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
    line-height: 1.4;
    color: #333333;
    word-break: break-all;
}

/* 用友经典金额样式 */
.uf-amount-cell {
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;
    font-size: 12px;
    font-weight: bold;
    color: #000000;
    text-align: right;
    padding: 2px 6px;
    letter-spacing: 0.5px;
}

.uf-currency {
    font-family: 'Times New Roman', serif;
    font-weight: bold;
    color: #000000;
    margin-right: 2px;
}

/* ========================================
   用友风格凭证状态管理样式
   ======================================== */

/* 状态徽章样式 - 用友经典设计 */
.uf-status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid;
    min-width: 48px;
    text-align: center;
    white-space: nowrap;
    line-height: 1.2;
    position: relative;
    /* 移除过度动画效果 */
}

.uf-status-badge:hover {
    /* 保持简洁，无悬停动画 */
}

/* 草稿状态 */
.uf-status-draft {
    background: linear-gradient(135deg, #F5F5F5, #E8E8E8);
    color: #666666;
    border-color: #CCCCCC;
}

.uf-status-draft::before {
    content: '📝';
    margin-right: 2px;
}

/* 待审核状态 */
.uf-status-pending {
    background: linear-gradient(135deg, #FFF8E1, #FFECB3);
    color: #E65100;
    border-color: #FFB74D;
    animation: uf-status-pulse 2s infinite;
}

.uf-status-pending::before {
    content: '⏳';
    margin-right: 2px;
}

/* 已审核状态 */
.uf-status-approved {
    background: linear-gradient(135deg, #E8F5E8, #C8E6C9);
    color: #1B5E20;
    border-color: #66BB6A;
}

.uf-status-approved::before {
    content: '✅';
    margin-right: 2px;
}

/* 已记账状态 */
.uf-status-posted {
    background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
    color: #0D47A1;
    border-color: #42A5F5;
}

.uf-status-posted::before {
    content: '📚';
    margin-right: 2px;
}

/* 已拒绝状态 */
.uf-status-rejected {
    background: linear-gradient(135deg, #FFEBEE, #FFCDD2);
    color: #B71C1C;
    border-color: #EF5350;
}

.uf-status-rejected::before {
    content: '❌';
    margin-right: 2px;
}

/* 状态脉冲动画 */
@keyframes uf-status-pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 状态流转图 */
.uf-status-flow {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: #F8F9FA;
    border: 1px solid #DDDDDD;
    border-radius: 6px;
    margin: 15px 0;
}

.uf-status-flow-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    min-width: 80px;
}

.uf-status-flow-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-bottom: 8px;
    border: 2px solid;
    transition: all 0.3s ease;
}

.uf-status-flow-label {
    font-size: 11px;
    color: #666666;
    text-align: center;
    font-weight: 600;
}

.uf-status-flow-arrow {
    position: absolute;
    right: -25px;
    top: 15px;
    font-size: 16px;
    color: #CCCCCC;
}

/* 状态流转项样式 */
.uf-status-flow-item.draft .uf-status-flow-icon {
    background: #F5F5F5;
    color: #666666;
    border-color: #CCCCCC;
}

.uf-status-flow-item.pending .uf-status-flow-icon {
    background: #FFF8E1;
    color: #E65100;
    border-color: #FFB74D;
}

.uf-status-flow-item.approved .uf-status-flow-icon {
    background: #E8F5E8;
    color: #1B5E20;
    border-color: #66BB6A;
}

.uf-status-flow-item.posted .uf-status-flow-icon {
    background: #E3F2FD;
    color: #0D47A1;
    border-color: #42A5F5;
}

.uf-status-flow-item.active .uf-status-flow-icon {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.uf-status-flow-item.completed .uf-status-flow-icon {
    background: #3FC8DD;
    color: #FFFFFF;
    border-color: #3FC8DD;
}

/* 状态操作按钮组 */
.uf-status-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-top: 15px;
    padding: 12px;
    background: #F8F9FA;
    border-radius: 6px;
    border: 1px solid #DDDDDD;
}

.uf-status-action-btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 3px;
    border: 1px solid;
    background: #FFFFFF;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    color: inherit;
}

.uf-status-action-btn:hover {
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.uf-status-action-submit {
    color: #1976D2;
    border-color: #1976D2;
}

.uf-status-action-submit:hover {
    background: #1976D2;
    color: #FFFFFF;
}

.uf-status-action-approve {
    color: #2E7D32;
    border-color: #2E7D32;
}

.uf-status-action-approve:hover {
    background: #2E7D32;
    color: #FFFFFF;
}

.uf-status-action-reject {
    color: #D32F2F;
    border-color: #D32F2F;
}

.uf-status-action-reject:hover {
    background: #D32F2F;
    color: #FFFFFF;
}

.uf-status-action-post {
    color: #7B1FA2;
    border-color: #7B1FA2;
}

.uf-status-action-post:hover {
    background: #7B1FA2;
    color: #FFFFFF;
}

/* 状态历史记录 */
.uf-status-history {
    background: #FFFFFF;
    border: 1px solid #DDDDDD;
    border-radius: 6px;
    margin-top: 15px;
}

.uf-status-history-header {
    background: #F2F4F6;
    padding: 10px 15px;
    border-bottom: 1px solid #DDDDDD;
    font-size: 13px;
    font-weight: 600;
    color: #333333;
}

.uf-status-history-item {
    padding: 12px 15px;
    border-bottom: 1px solid #F0F0F0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.uf-status-history-item:last-child {
    border-bottom: none;
}

.uf-status-history-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
}

.uf-status-history-content {
    flex: 1;
}

.uf-status-history-title {
    font-size: 12px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 2px;
}

.uf-status-history-desc {
    font-size: 11px;
    color: #666666;
    line-height: 1.4;
}

.uf-status-history-time {
    font-size: 11px;
    color: #999999;
    white-space: nowrap;
}

/* 凭证号样式 */
.uf-voucher-number {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    letter-spacing: 1px;
}

/* 摘要框样式 */
.uf-summary-box {
    background: #F8F9FA;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    padding: 8px 12px;
    font-size: 12px;
    color: #333333;
    line-height: 1.4;
    min-height: 32px;
}

/* 金额大写样式 */
.amount-words-row {
    background: #F2F4F6 !important;
    font-style: italic;
}

/* 表格空状态优化 */
.uf-table-empty {
    text-align: center;
    padding: 40px 20px;
    color: #999999;
    font-size: 14px;
    background: #F8F9FA;
    border: 1px solid #DDDDDD;
    border-radius: 6px;
}

.uf-table-empty i {
    font-size: 48px;
    color: #DDDDDD;
    margin-bottom: 16px;
    display: block;
}

/* ========================================
   用友风格打印样式
   ======================================== */

/* 打印页面设置 */
@page {
    size: A4 landscape;
    margin: 10mm 15mm;
}

@media print {
    .no-print {
        display: none !important;
    }

    body {
        padding: 0;
        margin: 0;
        font-family: '宋体', 'SimSun', serif !important;
        font-size: 12px !important;
        line-height: 1.4 !important;
        color: #000000 !important;
        background: white !important;
    }

    .voucher-container {
        box-shadow: none;
        padding: 0;
        max-width: none;
        width: 100%;
        margin: 0;
    }
}

/* 用友经典凭证信息行 */
.uf-print-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
    font-size: 14px;
}

.uf-print-info-group {
    display: flex;
    gap: 30px;
}

.uf-print-info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.uf-print-info-item label {
    font-weight: bold;
    min-width: 60px;
}

.uf-print-info-item .value {
    border-bottom: 1px solid #000000;
    padding: 2px 12px;
    min-width: 80px;
    text-align: center;
    font-weight: bold;
}

/* 用友经典凭证表格 */
.uf-print-table {
    width: 100%;
    border-collapse: collapse;
    border: 2px solid #000000;
    font-size: 12px;
    margin: 20px 0;
}

.uf-print-table th,
.uf-print-table td {
    border: 1px solid #000000;
    padding: 6px 4px;
    text-align: left;
    vertical-align: middle;
    height: 32px;
}

.uf-print-table th {
    background: #FFFFFF;
    font-weight: bold;
    text-align: center;
    font-size: 12px;
}

.uf-print-table .line-number {
    text-align: center;
    width: 8%;
}

.uf-print-table .subject-cell {
    width: 30%;
    font-size: 11px;
}

.uf-print-table .summary-cell {
    width: 30%;
    font-size: 11px;
}

.uf-print-table .amount-cell {
    text-align: right;
    font-family: 'Times New Roman', '宋体', serif;
    font-weight: bold;
    width: 16%;
    padding-right: 8px;
}

.uf-print-table .total-row {
    background: #F2F4F6;
    font-weight: bold;
    border-top: 2px solid #000000;
}

.uf-print-table .amount-words-row {
    background: #F8F9FA;
    font-style: italic;
    text-align: center;
}

/* 用友经典签名栏 */
.uf-print-signature {
    display: flex;
    justify-content: space-around;
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid #000000;
}

.uf-print-signature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.uf-print-signature-label {
    font-weight: bold;
    font-size: 12px;
}

.uf-print-signature-line {
    border-bottom: 1px solid #000000;
    width: 80px;
    height: 20px;
    display: block;
    text-align: center;
    font-size: 11px;
    padding-top: 2px;
}

/* 打印控制按钮 */
.print-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 10px;
    background: rgba(255, 255, 255, 0.95);
    padding: 12px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: 1px solid #DDDDDD;
}

.print-btn {
    padding: 8px 16px;
    background: #3FC8DD;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.print-btn:hover {
    background: #33A4B5;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.print-btn.landscape {
    background: #28A745;
}

.print-btn.landscape:hover {
    background: #218838;
}

/* 打印时的样式调整 */
@media print {
    .uf-print-table th {
        background: #FFFFFF !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        print-color-adjust: exact;
    }

    .uf-print-table .total-row {
        background: #F2F4F6 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        print-color-adjust: exact;
    }

    .uf-print-table .amount-words-row {
        background: #F8F9FA !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        print-color-adjust: exact;
    }
}

/* 操作按钮样式 - 用友经典设计 */
.uf-action-buttons {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 2px;
    width: 100%;
    margin: 0 auto;
    height: 100%;
    padding: 2px;
}

.uf-action-btn {
    display: inline-flex;
    width: 24px;
    height: 24px;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: #FFFFFF;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    padding: 0;
    margin: 0;
    font-size: 12px;
    color: #666666;
    cursor: pointer;
    text-decoration: none;
    /* 移除过度动画效果 */
}

.uf-action-btn:hover {
    background: #3FC8DD;
    color: #FFFFFF;
    border-color: #3FC8DD;
    text-decoration: none;
}

.uf-action-btn:active {
    background: #33A4B5;
    border-color: #33A4B5;
}

.uf-action-view {
    color: #1976D2;
}
.uf-action-view:hover {
    background: #1976D2;
    border-color: #1976D2;
    color: #FFFFFF;
}

.uf-action-edit {
    color: #FF8F00;
    font-weight: bold;
}
.uf-action-edit:hover {
    background: #FF8F00;
    border-color: #FF8F00;
    color: #FFFFFF;
}

.uf-action-delete {
    color: #F44336;
}
.uf-action-delete:hover {
    background: #F44336;
    border-color: #F44336;
    color: #FFFFFF;
}

.uf-action-submit {
    color: #1976D2;
    font-weight: bold;
    background: #E3F2FD;
    border-color: #1976D2;
}

.uf-action-approve {
    color: #2E7D32;
    font-weight: bold;
    background: #E8F5E8;
    border-color: #2E7D32;
}

.uf-action-reject {
    color: #F44336;
    font-weight: bold;
    background: #FFEBEE;
    border-color: #F44336;
}

/* 分页样式 */
.uf-pagination {
    background: #f8f9fa;
    border-top: 1px solid var(--uf-border);
    padding: 8px 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
}

.uf-page-link {
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
    border: 1px solid var(--uf-border);
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
    text-decoration: none;
    min-width: 30px;
    text-align: center;
}

.uf-page-link:hover {
    background: linear-gradient(to bottom, #e6f2ff, #cce5ff);
    color: var(--uf-primary);
    text-decoration: none;
}

.uf-page-active {
    background: linear-gradient(to bottom, var(--uf-primary), #1565c0);
    color: white;
    border-color: var(--uf-primary);
}

.uf-page-disabled {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
}

/* 空状态样式 */
.uf-empty-state {
    padding: 40px;
    text-align: center;
    color: #666;
    background: white;
}

.uf-empty-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.uf-empty-text {
    font-size: 14px;
    margin-bottom: 5px;
}

.uf-empty-hint {
    font-size: 12px;
    color: #999;
}

/* 状态栏样式 */
.uf-status-bar {
    background: #f5f5f5;
    border-top: 1px solid var(--uf-border);
    padding: 4px 15px;
    font-size: 13px;
    color: #666;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* ========================================
   财务凭证编辑页面样式
   ======================================== */

/* 凭证编辑容器 */
.voucher-edit-container {
    background: #FFFFFF;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    margin: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 凭证表头区域 */
.voucher-header-section {
    background: #F2F4F6;
    border-bottom: 1px solid #DDDDDD;
    padding: 12px 15px;
}

.voucher-header-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.voucher-header-title h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    margin: 0;
}

.voucher-header-form {
    background: #FFFFFF;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    padding: 8px 12px;
}

.voucher-header-form .form-label {
    font-size: 12px;
    color: #333333;
    font-weight: normal;
    margin-right: 4px;
}

.voucher-header-form .form-control {
    height: 24px;
    padding: 2px 6px;
    font-size: 12px;
    border: 1px solid #DDDDDD;
    border-radius: 2px;
    background: #FFFFFF;
}

.voucher-header-form .form-control:focus {
    border-color: #3FC8DD;
    outline: none;
    box-shadow: 0 0 0 1px rgba(63, 200, 221, 0.3);
}

/* 凭证表格区域 */
.voucher-table-section {
    padding: 15px;
}

.voucher-table {
    width: 100%;
    border-collapse: collapse;
    border: 2px solid #000000;
    font-family: '宋体', 'SimSun', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    font-size: 12px;
    background: #FFFFFF;
}

.voucher-table th {
    background: #FFFFFF;
    border: 1px solid #000000;
    padding: 8px 4px;
    text-align: center;
    font-weight: 600;
    color: #000000;
    font-size: 12px;
    vertical-align: middle;
}

.voucher-table td {
    border: 1px solid #000000;
    padding: 2px 4px;
    vertical-align: middle;
    background: #FFFFFF;
    height: 32px;
}

.voucher-table tbody tr:hover {
    background-color: #F8F9FA;
}

/* 金额单位显示 */
.amount-units {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #666666;
    margin-top: 2px;
    padding: 0 4px;
}

.amount-units span {
    width: 10%;
    text-align: center;
}

/* 表格输入框样式 */
.voucher-table .form-control {
    border: none;
    background: transparent;
    padding: 2px 4px;
    font-size: 12px;
    height: 28px;
    width: 100%;
    box-shadow: none;
}

.voucher-table .form-control:focus {
    background: #FFFACD;
    border: 1px solid #3FC8DD;
    outline: none;
}

.voucher-table .amount-input {
    font-family: 'Times New Roman', '宋体', 'SimSun', serif;
    text-align: right;
    letter-spacing: 1px;
    font-weight: bold;
}

/* 合计行样式 */
.voucher-table .total-row {
    background: #F2F4F6;
    font-weight: bold;
}

.voucher-table .total-row td {
    border-top: 2px solid #000000;
    padding: 6px 4px;
}

.voucher-table .amount-words-row {
    background: #F8F9FA;
}

.voucher-table .amount-words-row td {
    padding: 8px 4px;
    font-weight: bold;
    color: #333333;
}

/* 凭证操作按钮 */
.voucher-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #DDDDDD;
}

.voucher-actions .btn {
    height: 32px;
    padding: 0 16px;
    font-size: 12px;
    border-radius: 3px;
    border: 1px solid #DDDDDD;
    background: linear-gradient(to bottom, #FFFFFF, #F5F5F5);
    color: #333333;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.voucher-actions .btn:hover {
    background: linear-gradient(to bottom, #3FC8DD, #33A4B5);
    color: #FFFFFF;
    border-color: #3FC8DD;
}

.voucher-actions .btn-primary {
    background: linear-gradient(to bottom, #3FC8DD, #33A4B5);
    color: #FFFFFF;
    border-color: #3FC8DD;
}

.voucher-actions .btn-success {
    background: linear-gradient(to bottom, #28A745, #218838);
    color: #FFFFFF;
    border-color: #28A745;
}

.voucher-actions .btn-warning {
    background: linear-gradient(to bottom, #FFC107, #E0A800);
    color: #000000;
    border-color: #FFC107;
}

/* 签字区域 */
.voucher-signature-section {
    background: #F8F9FA;
    border-top: 1px solid #DDDDDD;
    padding: 12px 15px;
}

.signature-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.signature-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.signature-label {
    font-size: 12px;
    color: #333333;
    font-weight: normal;
    min-width: 40px;
}

.signature-box {
    display: inline-block;
    min-width: 80px;
    height: 24px;
    line-height: 22px;
    padding: 0 8px;
    border: 1px solid #DDDDDD;
    border-radius: 2px;
    background: #FFFFFF;
    font-size: 12px;
    color: #333333;
    text-align: center;
}

/* ========================================
   用友风格表格交互样式
   ======================================== */

/* 表格行状态 - 用友经典简洁风格 */
.uf-row-hover {
    background-color: #FFDEC9 !important;
}

.uf-row-current {
    background-color: #E3F2FD !important;
    border: 1px solid #3FC8DD !important;
}

.uf-row-current td {
    border-color: #3FC8DD !important;
}

/* 排序图标 */
.uf-sort-icon {
    margin-left: 4px;
    color: #666666;
    font-size: 10px;
    user-select: none;
}

.uf-voucher-list-table th[data-sortable] {
    cursor: pointer;
    user-select: none;
}

.uf-voucher-list-table th[data-sortable]:hover {
    background-color: #57B8CB !important;
}

.uf-voucher-list-table th[data-sortable]:hover .uf-sort-icon {
    color: #FFFFFF;
}

/* 右键菜单 */
.uf-context-menu {
    background: #FFFFFF;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    min-width: 120px;
    padding: 4px 0;
    font-size: 12px;
}

.uf-context-menu-item {
    padding: 6px 12px;
    cursor: pointer;
    color: #333333;
    transition: background-color 0.2s ease;
}

.uf-context-menu-item:hover {
    background-color: #3FC8DD;
    color: #FFFFFF;
}

/* 键盘导航提示 */
.uf-keyboard-hint {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: #FFFFFF;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 11px;
    z-index: 1000;
    display: none;
}

.uf-keyboard-hint.show {
    display: block;
}

/* 选择状态指示器 */
.uf-selection-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    background: #3FC8DD;
    color: #FFFFFF;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    display: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.uf-selection-indicator.show {
    display: block;
}

/* 表格焦点状态 */
.uf-voucher-list-table:focus-within {
    outline: 2px solid #3FC8DD;
    outline-offset: -2px;
}

/* 复选框样式增强 */
.uf-voucher-list-table input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: #3FC8DD;
}

.uf-voucher-list-table input[type="checkbox"]:indeterminate {
    background-color: #3FC8DD;
}

/* 排序状态指示 */
.uf-voucher-list-table th[data-sortable].sorted-asc .uf-sort-icon {
    color: #3FC8DD;
    font-weight: bold;
}

.uf-voucher-list-table th[data-sortable].sorted-desc .uf-sort-icon {
    color: #3FC8DD;
    font-weight: bold;
}

/* 表格加载状态 */
.uf-table-loading {
    position: relative;
}

.uf-table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.uf-table-loading::before {
    content: '加载中...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #3FC8DD;
    color: #FFFFFF;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 11;
}

/* 表格空状态 */
.uf-table-empty {
    text-align: center;
    padding: 40px 20px;
    color: #999999;
    font-size: 14px;
}

.uf-table-empty i {
    font-size: 48px;
    color: #DDDDDD;
    margin-bottom: 16px;
    display: block;
}

/* ========================================
   用友风格移动端响应式设计
   ======================================== */

/* 大屏幕设备优化 (1400px+) */
@media (min-width: 1400px) {
    .uf-voucher-list-window {
        max-width: 1600px;
    }

    .uf-voucher-list-table {
        width: 100%;
        min-width: 1400px;
    }

    /* 大屏幕下的列宽优化 */
    .uf-summary-col {
        width: 400px;
    }

    .uf-action-col {
        width: 220px;
    }
}

/* 平板设备适配 (768px - 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
    .uf-voucher-list-toolbar {
        padding: 8px 10px;
        gap: 6px;
    }

    .uf-voucher-list-toolbar .uf-btn {
        height: 30px;
        padding: 0 10px;
        font-size: 11px;
    }

    .uf-voucher-list-table {
        font-size: 11px !important;
    }

    .uf-voucher-list-table th,
    .uf-voucher-list-table td {
        padding: 3px 2px;
        font-size: 11px !important;
    }

    .uf-action-btn {
        width: 22px;
        height: 22px;
        font-size: 11px;
    }
}

/* 手机设备适配 (最大768px) */
@media (max-width: 768px) {
    /* 搜索栏移动端优化 */
    .uf-voucher-search-bar {
        padding: 6px 8px;
        border-radius: 0;
    }

    .uf-search-form {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .uf-search-group {
        flex-direction: column;
        gap: 4px;
        align-items: stretch;
    }

    .uf-search-label {
        font-size: 11px;
        min-width: auto;
    }

    .uf-search-input,
    .uf-search-select,
    .uf-search-date {
        width: 100%;
        height: 32px;
        font-size: 14px;
    }

    /* 工具栏移动端优化 */
    .uf-voucher-list-toolbar {
        padding: 6px 8px;
        flex-wrap: wrap;
        gap: 4px;
        min-height: auto;
    }

    .uf-voucher-list-toolbar .uf-btn {
        height: 32px;
        padding: 0 8px;
        font-size: 12px;
        flex: 1;
        min-width: 80px;
        justify-content: center;
    }

    .uf-toolbar-spacer {
        display: none;
    }

    /* 表格移动端优化 */
    .uf-voucher-list-table {
        font-size: 10px !important;
        min-width: 600px; /* 保持最小宽度，允许横向滚动 */
    }

    .uf-voucher-list-table th,
    .uf-voucher-list-table td {
        padding: 4px 2px;
        font-size: 10px !important;
        white-space: nowrap;
    }

    /* 列宽移动端调整 */
    .uf-checkbox-col { width: 30px; }
    .uf-voucher-number-col { width: 100px; }
    .uf-date-col { width: 60px; }
    .uf-type-col { width: 40px; }
    .uf-summary-col { width: 150px; }
    .uf-amount-col { width: 80px; }
    .uf-status-col { width: 50px; }
    .uf-action-col { width: 100px; }

    /* 操作按钮移动端优化 */
    .uf-action-buttons {
        gap: 1px;
        padding: 1px;
    }

    .uf-action-btn {
        width: 18px;
        height: 18px;
        font-size: 9px;
        min-width: 18px;
        max-width: 18px;
    }

    /* 状态徽章移动端优化 */
    .uf-status-badge {
        font-size: 9px;
        padding: 1px 3px;
        min-width: 28px;
        line-height: 1.1;
    }

    /* 凭证编辑页面移动端优化 */
    .voucher-edit-container {
        margin: 4px;
        border-radius: 0;
    }

    .voucher-header-section {
        padding: 8px 10px;
    }

    .voucher-header-form .row {
        flex-direction: column;
        gap: 8px;
    }

    .voucher-header-form .col-auto {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .voucher-header-form .form-control {
        width: 60%;
        max-width: 150px;
    }

    /* 凭证表格移动端优化 */
    .voucher-table-section {
        padding: 8px;
        overflow-x: auto;
    }

    .voucher-table {
        min-width: 600px;
        font-size: 11px;
    }

    .voucher-table th,
    .voucher-table td {
        padding: 4px 2px;
        height: 28px;
    }

    /* 凭证操作按钮移动端优化 */
    .voucher-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 6px;
    }

    .voucher-actions .btn {
        height: 36px;
        font-size: 13px;
        justify-content: center;
    }

    /* 状态流转图移动端优化 */
    .uf-status-flow {
        flex-direction: column;
        gap: 8px;
        padding: 12px;
    }

    .uf-status-flow-item {
        flex-direction: row;
        align-items: center;
        gap: 12px;
        min-width: auto;
    }

    .uf-status-flow-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
        margin-bottom: 0;
    }

    .uf-status-flow-arrow {
        display: none;
    }

    /* 状态操作按钮移动端优化 */
    .uf-status-actions {
        flex-direction: column;
        gap: 6px;
        align-items: stretch;
    }

    .uf-status-action-btn {
        height: 36px;
        justify-content: center;
        font-size: 13px;
    }

    /* 状态历史记录移动端优化 */
    .uf-status-history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
        padding: 10px 12px;
    }

    .uf-status-history-icon {
        align-self: flex-start;
    }

    .uf-status-history-time {
        align-self: flex-end;
        font-size: 10px;
    }

    /* 签名区域移动端优化 */
    .signature-row {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .signature-item {
        justify-content: space-between;
    }

    /* 分页移动端优化 */
    .uf-pagination {
        flex-wrap: wrap;
        gap: 4px;
        justify-content: center;
    }

    .uf-page-link {
        min-width: 32px;
        height: 32px;
        font-size: 12px;
        padding: 0 6px;
    }

    /* 模态框移动端优化 */
    .modal-dialog {
        margin: 10px;
        max-width: calc(100vw - 20px);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 12px;
    }

    .modal-title {
        font-size: 16px;
    }

    /* 表格横向滚动容器 */
    .table-responsive {
        border: none;
        margin-bottom: 0;
    }

    .table-responsive::-webkit-scrollbar {
        height: 6px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: #F5F5F5;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: #CCCCCC;
        border-radius: 3px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #999999;
    }
}

/* 超小屏幕设备适配 (最大480px) */
@media (max-width: 480px) {
    .uf-voucher-list-table {
        font-size: 9px !important;
    }

    .uf-voucher-list-table th,
    .uf-voucher-list-table td {
        padding: 2px 1px;
        font-size: 9px !important;
    }

    .uf-action-btn {
        width: 16px;
        height: 16px;
        font-size: 8px;
    }

    .uf-status-badge {
        font-size: 8px;
        padding: 1px 2px;
        min-width: 24px;
    }

    .voucher-header-form .form-control {
        width: 50%;
        max-width: 120px;
        font-size: 12px;
    }

    .uf-status-flow-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .uf-status-action-btn {
        height: 32px;
        font-size: 12px;
        padding: 0 8px;
    }
}

/* ========================================
   用友风格用户体验增强样式
   ======================================== */

/* 工具提示样式 */
.uf-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: #FFFFFF;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 10000;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.2s ease;
    pointer-events: none;
}

.uf-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.uf-tooltip::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid rgba(0, 0, 0, 0.8);
}

/* 加载遮罩样式 */
.uf-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.uf-loading-overlay.show {
    opacity: 1;
}

.uf-loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.uf-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #F3F3F3;
    border-top: 3px solid #3FC8DD;
    border-radius: 50%;
    animation: uf-spin 1s linear infinite;
}

.uf-loading-text {
    font-size: 14px;
    color: #666666;
    font-weight: 500;
}

@keyframes uf-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 表单验证样式 */
.uf-field-error {
    border-color: #F44336 !important;
    box-shadow: 0 0 0 1px rgba(244, 67, 54, 0.3) !important;
}

.uf-field-error-message {
    color: #F44336;
    font-size: 11px;
    margin-top: 4px;
    display: block;
}

/* 通知样式 */
.uf-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    z-index: 10000;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    min-width: 200px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.uf-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.uf-notification-success {
    background: #E8F5E8;
    color: #2E7D32;
    border-left: 4px solid #4CAF50;
}

.uf-notification-error {
    background: #FFEBEE;
    color: #C62828;
    border-left: 4px solid #F44336;
}

.uf-notification-warning {
    background: #FFF8E1;
    color: #F57C00;
    border-left: 4px solid #FF9800;
}

.uf-notification-info {
    background: #E3F2FD;
    color: #1976D2;
    border-left: 4px solid #2196F3;
}

/* 模态框样式 */
.uf-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.uf-modal-overlay.show {
    opacity: 1;
}

.uf-modal {
    background: #FFFFFF;
    border-radius: 6px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.uf-modal-overlay.show .uf-modal {
    transform: scale(1);
}

.uf-modal-header {
    background: #F2F4F6;
    padding: 15px 20px;
    border-bottom: 1px solid #DDDDDD;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.uf-modal-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333333;
}

.uf-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #666666;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.uf-modal-close:hover {
    color: #333333;
}

.uf-modal-body {
    padding: 20px;
    font-size: 14px;
    line-height: 1.6;
    color: #333333;
}

/* 按钮样式 - 用友经典简洁风格 */
.uf-btn,
.uf-action-btn {
    position: relative;
    /* 移除过度动画效果，保持用友风格简洁性 */
}

/* 页面加载动画 */
.uf-page-loading {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.uf-page-loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* 表格行悬停 - 用友经典简洁风格 */
.uf-voucher-list-table tbody tr {
    /* 移除过度动画，保持用友风格的简洁性 */
}

.uf-voucher-list-table tbody tr:hover {
    /* 仅保留背景色变化，符合用友经典设计 */
    background-color: #FFDEC9 !important;
}

/* 焦点增强 */
.form-control:focus,
.uf-search-input:focus,
.uf-search-select:focus,
.uf-search-date:focus {
    border-color: #3FC8DD;
    outline: none;
    box-shadow: 0 0 0 2px rgba(63, 200, 221, 0.2);
    transition: all 0.2s ease;
}

/* 帮助按钮样式 */
.uf-help-btn {
    background: #F8F9FA;
    border: 1px solid #DDDDDD;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666666;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s ease;
}

.uf-help-btn:hover {
    background: #3FC8DD;
    color: #FFFFFF;
    border-color: #3FC8DD;
    text-decoration: none;
}

/* 键盘导航增强 */
.uf-keyboard-focus {
    outline: 2px solid #3FC8DD;
    outline-offset: 2px;
}

/* 无障碍增强 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
